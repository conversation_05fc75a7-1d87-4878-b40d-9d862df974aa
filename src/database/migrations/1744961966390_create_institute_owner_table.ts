import { sql, type Kysely } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable("instituteOwner")
    .addColumn("userId", "uuid", col => col.notNull().primaryKey())
    .addColumn("hasCompletedSetup", "boolean", col =>
      col.notNull().defaultTo(false),
    )
    .addColumn("createdAt", "timestamptz", col =>
      col.notNull().defaultTo(sql`(NOW())`),
    )
    .addForeignKeyConstraint("owner_userId", ["userId"], "users", ["id"], cb =>
      cb.onDelete("restrict").onUpdate("cascade"),
    )
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("instituteOwner").execute();
}
