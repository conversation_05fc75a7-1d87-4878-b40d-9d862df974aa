import { DynamicModule, Module, Provider } from "@nestjs/common";
import {
  DrizzleModuleAsyncOptions,
  DrizzlePostgresConfig,
} from "./drizzle.interface.js";
import {
  DRIZZLE_MODULE_CONFIG_TOKEN,
  DRIZ<PERSON>LE_MODULE_CONNECTION_TOKEN,
} from "./drizzle.constants.js";
import { DrizzleService } from "./drizzle.service.js";
import { createDrizzleClient } from "./drizzle.providers.js";

@Module({})
export class DrizzleModule {
  public static forRootAsync(
    options: DrizzleModuleAsyncOptions,
  ): DynamicModule {
    const provider: Provider = {
      inject: [DRIZZLE_MODULE_CONFIG_TOKEN, DrizzleService],
      provide: DRIZZLE_MODULE_CONNECTION_TOKEN,
      useFactory: (config: DrizzlePostgresConfig, service: DrizzleService) => {
        return createDrizzleClient(config, service);
      },
    };

    return {
      global: true,
      exports: [provider],
      module: DrizzleModule,
      providers: [
        {
          inject: options.inject,
          provide: DRIZZLE_MODULE_CONFIG_TOKEN,
          useFactory: options.useFactory,
        },
        DrizzleService,
        provider,
      ],
      imports: options.imports,
    };
  }
}
