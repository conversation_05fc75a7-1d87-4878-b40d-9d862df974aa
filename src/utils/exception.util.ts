interface ErrorOptions {
  cause?: unknown;
}

export interface PostgresExceptionOptions extends ErrorOptions {
  code: string;
  severity: string;
  constraint?: string;
  detail?: string;
}

export class PostgresException extends Error {
  public readonly code: string;
  public readonly severity: string;
  public readonly constraint?: string;
  public readonly detail?: string;

  public constructor(
    public readonly message: string,
    options: PostgresExceptionOptions,
  ) {
    super(message, { cause: options.cause });
    this.code = options.code;
    this.severity = options.severity;
    this.constraint = options.constraint;
    this.detail = options.detail;
  }
}

export interface PostgresUniqueConstraintException extends PostgresException {
  readonly constraint: string;
  readonly detail: string;
}

export function isPostgresException(error: any): error is PostgresException {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-member-access
  return error.code && error.severity === "ERROR";
}
