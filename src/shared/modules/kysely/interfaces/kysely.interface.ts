/* eslint-disable @typescript-eslint/no-unnecessary-type-arguments */
import { Abstract, ModuleMetadata, Type } from "@nestjs/common";
import { KyselyConfig } from "kysely";

type InjectType = (
  | string
  | symbol
  | Type<any>
  | Abstract<any>
  | (() => void)
)[];

export interface KyselyModuleOptionsFactory {
  createKyselyModuleOptions(): Promise<KyselyConfig> | KyselyConfig;
}

export interface KyselyModuleAsyncOptions
  extends Pick<ModuleMetadata, "imports"> {
  inject?: InjectType;
  useFactory: (...args: any[]) => Promise<KyselyConfig> | KyselyConfig;
}
