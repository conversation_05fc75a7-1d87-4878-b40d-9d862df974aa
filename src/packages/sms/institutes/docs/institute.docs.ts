import { ApiOperation, ApiResponse } from "@nestjs/swagger";
import { useCommonCreateResourceApiResponses } from "../../../../docs/shared/api-responses.js";
import { InstituteResponseDto } from "../dto/institute.dto.js";

export function ApiDocCreateInstitute() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Create a new Institute",
      description: "Creates a new institute with the provided details",
    }),
    ApiResponse({
      status: 201,
      type: InstituteResponseDto,
      description: "Returns the newly created institute with complete details",
    }),
  );
}

export function ApiDocGetAllInstitutes() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Get all institutes",
      description: "Returns all institutes",
    }),
    ApiResponse({
      status: 200,
      type: [InstituteResponseDto],
      isArray: true,
      description: "Returns the list of institutes",
    }),
  );
}

export function ApiDocGetOwnerInstitute() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Get all institutes",
      description: "Returns all institutes",
    }),
    ApiResponse({
      status: 200,
      type: InstituteResponseDto,
      description: "Returns the list of institutes",
    }),
  );
}
