import { defineConfig } from "drizzle-kit";

/**
 * Drizzle configuration for PostgreSQL database
 *
 * This configuration file defines the settings for Drizzle ORM
 * including database connection, schema location, and migration settings.
 */
export default defineConfig({
  // Database connection
  dialect: "postgresql",
  dbCredentials: {
    url: process.env.DATABASE_URL!,
  },

  // Schema configuration
  schema: "./src/db/schemas",

  casing: "snake_case",

  // Migration configuration
  out: "./src/db/migrations",

  // Additional options
  verbose: true,
  strict: true,

  // Introspection settings
  introspect: {
    casing: "camel",
  },

  // Migration settings
  migrations: {
    prefix: "timestamp",
    table: "__drizzle_migrations__",
    schema: "public",
  },
});
