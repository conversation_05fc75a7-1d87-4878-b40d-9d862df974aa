import type { Response } from "express";
import { jwtConstants } from "../constants/jwt-constants.js";

export function setCookie(response: Response, refreshToken: string) {
  response.cookie("refreshToken", refreshToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "lax",
    maxAge: jwtConstants.refreshToken.expireTimeInSec * 1000, // in milliseconds
  });
}
