import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Param,
  Post,
  Res,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { ClassSectionsService } from "./class-sections.service.js";
import { Roles } from "../../../../core/roles/decorators/roles.decorator.js";
import { ClassIdParamDto } from "./dto/class-sections-params.dto.js";
import { CreateClassSectionDto } from "./dto/class-sections.dto.js";
import type { Response } from "express";

@Controller("sms/classes")
@ApiTags("Class Sections")
export class ClassSectionsController {
  public constructor(private readonly sectionsService: ClassSectionsService) {}

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Post("/:classId/sections")
  public async create(
    @Body() createClassSectionDto: CreateClassSectionDto,
    @Param() param: ClassIdParamDto,
    @Res() res: Response,
  ) {
    const { id } = await this.sectionsService.create({
      ...createClassSectionDto,
      classId: param.classId,
    });
    res.setHeader("Location", id);
    res.status(HttpStatus.CREATED).send();
  }

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Get("/:classId/sections")
  public getAllClassSections(@Param() param: ClassIdParamDto) {
    return this.sectionsService.getAllByClassId(param.classId);
  }
}
