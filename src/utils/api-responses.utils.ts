import { HttpStatus } from "@nestjs/common";
import { ApiResponse } from "../shared/interceptors/transformResponseInterceptor.js";

export function createBadRequestResponse(message: string): ApiResponse<null> {
  return {
    statusCode: HttpStatus.BAD_REQUEST,
    message,
    data: null,
  };
}

export function createNotFoundResponse(message: string): ApiResponse<null> {
  return {
    statusCode: HttpStatus.NOT_FOUND,
    message,
    data: null,
  };
}

export function createConflictResponse(message: string): ApiResponse<null> {
  return {
    statusCode: HttpStatus.CONFLICT,
    message,
    data: null,
  };
}

export function createInternalServerErrorResponse(
  message: string,
): ApiResponse<null> {
  return {
    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
    message,
    data: null,
  };
}

export function createForbiddenResponse(message: string): ApiResponse<null> {
  return {
    statusCode: HttpStatus.FORBIDDEN,
    message,
    data: null,
  };
}
