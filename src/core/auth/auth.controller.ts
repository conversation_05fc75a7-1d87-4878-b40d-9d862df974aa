import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Post,
  Req,
  Res,
} from "@nestjs/common";
import { AuthService } from "./auth.service.js";
import { SignInDto } from "./dto/login-user.dto.js";
import { ApiTags } from "@nestjs/swagger";
import type { Response, Request } from "express";
import { Public } from "../../shared/decorators/public.decorator.js";
import { SetPasswordDto } from "./dto/set-password.dto.js";
import { setCookie } from "./utils/auth.utils.js";
import {
  ApiDocSmsLogin,
  ApiDocPlatformLogin,
  ApiDocSetPassword,
  ApiDocRefreshToken,
} from "./docs/auth.docs.js";

@ApiTags("Authentication")
@Controller("")
export class AuthController {
  public constructor(private readonly authService: AuthService) {}

  @Public()
  @Post("sms/auth/login")
  @HttpCode(HttpStatus.OK)
  @ApiDocSmsLogin()
  public async login(
    @Body() signInDto: SignInDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    const { accessToken, role, refreshToken } =
      await this.authService.smsUsersLogin(signInDto);

    if (refreshToken) {
      setCookie(response, refreshToken);
    }

    return {
      accessToken,
      role,
      refreshToken,
    };
  }

  @Public()
  @Post("platform/auth/login")
  @HttpCode(HttpStatus.OK)
  @ApiDocPlatformLogin()
  public async platformLogin(
    @Body() signInDto: SignInDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    const { accessToken, role, refreshToken } =
      await this.authService.platformAdminLogin(signInDto);

    setCookie(response, refreshToken);

    return {
      accessToken,
      role,
    };
  }

  @Post("sms/auth/set-password")
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiDocSetPassword()
  public async setPassword(
    @Body() setPasswordDto: SetPasswordDto,
    @Req() req: Request,
  ) {
    const userId = req.user.sub;
    await this.authService.setPassword({
      userId,
      password: setPasswordDto.password,
    });
  }

  @Public()
  @Post("sms/auth/refresh-token")
  @HttpCode(HttpStatus.OK)
  @ApiDocRefreshToken()
  public async refreshToken(@Req() req: Request) {
    const refreshToken = req.cookies.refreshToken as string | undefined;
    const { accessToken, role } =
      await this.authService.refreshToken(refreshToken);
    return {
      accessToken,
      role,
    };
  }
}
