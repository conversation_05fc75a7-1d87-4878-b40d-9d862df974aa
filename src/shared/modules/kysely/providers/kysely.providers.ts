import { Kysely, KyselyConfig } from "kysely";
import { KYSELY_MODULE_CONNECTION_TOKEN } from "../constants/kysely.constants.js";
import { KyselyService } from "../services/kysely.service.js";
import { Provider } from "@nestjs/common";

export function createKyselyClient<DB>(
  config: KyselyConfig,
  service: KyselyService,
): Kysely<DB> {
  const client = new Kysely<DB>(config);
  service.addClient(client);
  return client;
}

export function createKyselyProviders(config: KyselyConfig): Provider[] {
  return [
    {
      inject: [KyselyService],
      provide: KYSELY_MODULE_CONNECTION_TOKEN,
      useFactory: (keyselyService: KyselyService) => {
        return createKyselyClient(config, keyselyService);
      },
    } as const,
  ];
}
