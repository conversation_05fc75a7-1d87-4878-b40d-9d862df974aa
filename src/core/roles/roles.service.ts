import { Injectable, Logger } from "@nestjs/common";
import { InjectKysely } from "../../shared/modules/kysely/decorators/kysely.decorators.js";
import { Kysely, Transaction } from "kysely";
import { DB } from "../../database/types.js";
import { NewRole, Role } from "./types/roles.types.js";
import { ROLES } from "./constants/roles.constants.js";

@Injectable()
export class RolesService {
  private readonly logger = new Logger(RolesService.name);

  public constructor(@InjectKysely() public db: Kysely<DB>) {}

  public async create(role: NewRole, trx?: Transaction<DB>) {
    const kyselyClient = trx ?? this.db;
    try {
      return await kyselyClient
        .insertInto("role")
        .values(role)
        .returningAll()
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      this.logger.error("Failed to create role", error);
      throw error;
    }
  }

  public async findById(id: Role["id"]) {
    try {
      return await this.db
        .selectFrom("role")
        .where("id", "=", id)
        .selectAll()
        .executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error(`Failed to get role by id: ${id}`, error);
      throw error;
    }
  }

  public async findByNameOrCreate(
    name: Role["name"],
    trx?: Transaction<DB>,
  ): Promise<Role> {
    try {
      const kyselyClient = trx ?? this.db;
      const role = await kyselyClient
        .selectFrom("role")
        .where("name", "=", name)
        .selectAll()
        .executeTakeFirst();
      if (!role) {
        return await this.create(ROLES[name], trx);
      }
      return role;
    } catch (error: unknown) {
      this.logger.error(`Failed to get role by name: ${name}`, error);
      throw error;
    }
  }
}
