import { Injectable } from "@nestjs/common";
import { EnvService } from "../../services/env/env.service.js";

@Injectable()
export class FileUploadService {
  public constructor(private readonly envService: EnvService) {}

  public handleFileUpload(file: Express.Multer.File) {
    const filePath = `${this.envService.get("API_URL")}/${this.envService.get("UPLOADS_DIR")}/${file.filename}`;
    return { url: filePath };
  }
}
