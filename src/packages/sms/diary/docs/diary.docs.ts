import {
  Api<PERSON><PERSON>ation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
} from "@nestjs/swagger";
import { applyDecorators } from "@nestjs/common";
import { CreateDiaryDto } from "../dto/diary.dto.js";
import { commonApiResponseOptions } from "../../../../docs/shared/api-responses.js";

/**
 * Swagger documentation for the create diary endpoint
 * Creates a new diary entry for a specific class section
 */
export function ApiDocCreateDiary() {
  return applyDecorators(
    ApiOperation({
      summary: "Create Diary Entry",
      description: `
        Creates a new diary entry for a specific class section.
        
        **Required Information:**
        - **Section ID**: Must be a valid class section ID (provided in URL path)
        - **Subject ID**: Must be a valid subject ID that exists in the system
        - **Content**: The diary entry content describing what was taught or activities performed
        - **Date**: The date when the diary entry is being recorded (YYYY-MM-DD format)

        **Authentication Required:** Bearer token must be provided in the Authorization header.

        **Permissions:** Only Institute Owners, Branch Admins, and Teachers can create diary entries.

        **Validation Rules:**
        - Subject must exist and be valid
        - Class section must exist and be accessible to the user
        - Date must be in valid YYYY-MM-DD format
        - Content cannot be empty

        **Use Cases:**
        - Teachers recording daily lesson plans and activities
        - Documenting what was covered in each subject for a class section
        - Tracking educational progress and curriculum completion
      `,
    }),
    ApiParam({
      name: "sectionId",
      required: true,
      description: "Unique identifier of the class section",
      type: String,
      example: "123e4567-e89b-12d3-a456-426614174000",
    }),
    ApiBody({
      type: CreateDiaryDto,
      description: "Diary entry data including subject, content, and date",
      examples: {
        "mathematics-lesson": {
          summary: "Mathematics Lesson Entry",
          description: "Example diary entry for a mathematics lesson",
          value: {
            subjectId: "456e7890-e89b-12d3-a456-426614174001",
            content:
              "Covered quadratic equations and their applications. Students practiced solving problems using the quadratic formula. Assigned homework: Chapter 5, exercises 1-15.",
            date: "2024-01-15",
          },
        },
        "science-experiment": {
          summary: "Science Experiment Entry",
          description: "Example diary entry for a science practical session",
          value: {
            subjectId: "789e0123-e89b-12d3-a456-426614174002",
            content:
              "Conducted experiment on photosynthesis. Students observed oxygen bubble formation in aquatic plants under different light conditions. Lab report due next class.",
            date: "2024-01-15",
          },
        },
      },
    }),
    ApiResponse({
      status: 201,
      description: "Diary entry created successfully",
      schema: {
        type: "object",
        properties: {
          statusCode: { type: "number", example: 201 },
          message: {
            type: "string",
            example: "Diary entry created successfully",
          },
          data: {
            type: "object",
            properties: {
              id: {
                type: "string",
                example: "abc123def-456g-789h-012i-345678901234",
              },
            },
          },
        },
      },
    }),
    ApiResponse(commonApiResponseOptions.BAD_REQUEST),
    ApiResponse(commonApiResponseOptions.FORBIDDEN),
    ApiResponse(commonApiResponseOptions.NOT_FOUND),
    ApiResponse(commonApiResponseOptions.INTERNAL_SERVER_ERROR),
  );
}

/**
 * Swagger documentation for the get all diaries endpoint
 * Retrieves diary entries for a specific class section with filtering options
 */
export function ApiDocGetAllDiaries() {
  return applyDecorators(
    ApiOperation({
      summary: "Get Diary Entries",
      description: `
        Retrieves all diary entries for a specific class section with comprehensive filtering options.

        **Filtering Options:**
        - **By Specific Date**: Use \`date\` parameter to get entries for a particular day
        - **By Month**: Use \`month\` parameter to get all entries for a specific month
        - **By Date Range**: Use \`from\` and \`to\` parameters together for a custom date range
        - **By Subject**: Use \`subjectId\` parameter to filter entries for a specific subject

        **Date Filter Rules:**
        - Only one date filter type can be used at a time (date, month, or from/to range)
        - When using date range, \`from\` date must be earlier than or equal to \`to\` date
        - Date format must be YYYY-MM-DD for \`date\`, \`from\`, and \`to\` parameters
        - Month format must be YYYY-MM for \`month\` parameter

        **Pagination:**
        - Use \`limit\` to control the number of entries returned (default: 10)
        - Use \`offset\` to skip entries for pagination (default: 0)

        **Sorting:**
        - Results are sorted by date (most recent first)
        - Secondary sort by creation time (most recent first)

        **Authentication Required:** Bearer token must be provided in the Authorization header.

        **Permissions:** Only Institute Owners, Branch Admins, and Teachers can view diary entries.

        **Response Structure:**
        - Returns paginated list of diary entries with complete details
        - Includes subject information (name, type, marks)
        - Includes class section and class information
        - Provides total count for pagination
      `,
    }),
    ApiParam({
      name: "sectionId",
      required: true,
      description: "Unique identifier of the class section",
      type: String,
      example: "123e4567-e89b-12d3-a456-426614174000",
    }),
    ApiQuery({
      name: "date",
      required: false,
      description:
        "Filter by specific date (YYYY-MM-DD format). Cannot be used with other date filters.",
      type: String,
      example: "2024-01-15",
    }),
    ApiQuery({
      name: "month",
      required: false,
      description:
        "Filter by specific month (YYYY-MM format). Cannot be used with other date filters.",
      type: String,
      example: "2024-01",
    }),
    ApiQuery({
      name: "from",
      required: false,
      description:
        "Start date for date range filter (YYYY-MM-DD format). Must be used with 'to' parameter.",
      type: String,
      example: "2024-01-01",
    }),
    ApiQuery({
      name: "to",
      required: false,
      description:
        "End date for date range filter (YYYY-MM-DD format). Must be used with 'from' parameter.",
      type: String,
      example: "2024-01-31",
    }),
    ApiQuery({
      name: "subjectId",
      required: false,
      description: "Filter by specific subject ID",
      type: String,
      example: "456e7890-e89b-12d3-a456-426614174001",
    }),
    ApiQuery({
      name: "limit",
      required: false,
      description: "Maximum number of diary entries to retrieve",
      type: Number,
      example: 10,
    }),
    ApiQuery({
      name: "offset",
      required: false,
      description: "Number of diary entries to skip for pagination",
      type: Number,
      example: 0,
    }),
    ApiResponse({
      status: 200,
      description: "Successfully retrieved diary entries",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              statusCode: { type: "number", example: 200 },
              message: {
                type: "string",
                example: "Diary entries retrieved successfully",
              },
              data: {
                type: "object",
                properties: {
                  items: {
                    type: "array",
                    items: { $ref: "#/components/schemas/DiaryResponseDto" },
                  },
                  total: { type: "number", example: 25 },
                },
              },
            },
          },
          examples: {
            "filtered-by-date": {
              summary: "Filtered by Specific Date",
              description: "Example response when filtering by a specific date",
              value: {
                statusCode: 200,
                message: "Diary entries retrieved successfully",
                data: {
                  items: [
                    {
                      id: "diary-123",
                      content:
                        "Covered quadratic equations and their applications. Students practiced solving problems using the quadratic formula.",
                      date: "2024-01-15T00:00:00.000Z",
                      createdAt: "2024-01-15T10:30:00.000Z",
                      subject: {
                        id: "subject-math",
                        name: "Mathematics",
                        type: "THEORY",
                        marks: 100,
                      },
                      classSection: {
                        id: "section-123",
                        name: "Section A",
                        class: {
                          id: "class-456",
                          name: "Grade 10",
                        },
                      },
                    },
                  ],
                  total: 1,
                },
              },
            },
            "filtered-by-month": {
              summary: "Filtered by Month",
              description: "Example response when filtering by month",
              value: {
                statusCode: 200,
                message: "Diary entries retrieved successfully",
                data: {
                  items: [
                    {
                      id: "diary-456",
                      content:
                        "Introduction to organic chemistry. Discussed carbon bonding and basic organic compounds.",
                      date: "2024-01-20T00:00:00.000Z",
                      createdAt: "2024-01-20T14:15:00.000Z",
                      subject: {
                        id: "subject-chemistry",
                        name: "Chemistry",
                        type: "THEORY",
                        marks: 100,
                      },
                      classSection: {
                        id: "section-123",
                        name: "Section A",
                        class: {
                          id: "class-456",
                          name: "Grade 10",
                        },
                      },
                    },
                  ],
                  total: 15,
                },
              },
            },
          },
        },
      },
    }),
    ApiResponse(commonApiResponseOptions.BAD_REQUEST),
    ApiResponse(commonApiResponseOptions.FORBIDDEN),
    ApiResponse(commonApiResponseOptions.NOT_FOUND),
    ApiResponse(commonApiResponseOptions.INTERNAL_SERVER_ERROR),
  );
}
