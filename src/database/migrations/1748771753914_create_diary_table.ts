import { type Kysely, sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable("diary")
    .addColumn("id", "uuid", col =>
      col.primaryKey().defaultTo(sql`gen_random_uuid()`),
    )
    .addColumn("classSectionId", "uuid", col =>
      col
        .notNull()
        .references("classSection.id")
        .onDelete("restrict")
        .onUpdate("cascade"),
    )
    .addColumn("subjectId", "uuid", col =>
      col
        .notNull()
        .references("subject.id")
        .onDelete("restrict")
        .onUpdate("cascade"),
    )
    .addColumn("content", "text", col => col.notNull())
    .addColumn("date", "date", col => col.notNull())
    .addColumn("createdAt", "timestamptz", col =>
      col.notNull().defaultTo(sql`NOW()`),
    )
    .addUniqueConstraint("unique_diary_entry", [
      "classSectionId",
      "subjectId",
      "date",
    ])
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("diary").execute();
}
