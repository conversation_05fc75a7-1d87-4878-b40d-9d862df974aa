import { z } from "zod";
import {
  dateSchema,
  getUuidSchema,
} from "../../../../../shared/schema/zod-common.schema.js";
import { createZodDto } from "nestjs-zod";

export const enrollmentStatusSchema = z.enum(
  ["ACTIVE", "EXPELLED", "GRADUATED", "DECEASED", "COMPLETED", "WITHDRAWN"],
  {
    message:
      "Enrollment status must be one of 'ACTIVE', 'EXPELLED', 'GRADUATED', 'DECEASED', 'COMPLETED', or 'WITHDRAWN'.",
  },
);
export const enrollmentTypeSchema = z.enum(
  ["ADMISSION", "TRANSFER_IN", "REPEATING", "PROMOTION"],
  {
    message:
      "Enrollment type must be one of 'ADMISSION', 'TRANSFER_IN', 'REPEATING', or 'PROMOTION'.",
  },
);
const enrollmentBaseSchema = z.object({
  studentId: getUuidSchema("Student ID"),
  classSectionId: getUuidSchema("Class Section ID"),
  academicSessionId: getUuidSchema("Academic Session ID"),
  type: enrollmentTypeSchema,
  status: enrollmentStatusSchema,
  date: dateSchema,
});

// ------------------- Create-Enrollment-Dto ------------------->
const createEnrollmentSchema = enrollmentBaseSchema.omit({
  academicSessionId: true,
});
export class CreateEnrollmentDto extends createZodDto(createEnrollmentSchema) {}
