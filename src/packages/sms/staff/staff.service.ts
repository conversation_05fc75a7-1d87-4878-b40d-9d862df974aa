import {
  Injectable,
  Logger,
  NotFoundException,
  OnModuleInit,
} from "@nestjs/common";
import { InjectKysely } from "../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { DB } from "../../../database/types.js";
import { Kysely } from "kysely";
import {
  CreateInstitutionStaffPayload,
  CreateStaffPayload,
  ListStaffQueryOptions,
  Staff,
  StaffProfile,
  UpdateStaffPayload,
} from "./types/staff.types.js";
import {
  extractKeysFromObject,
  omitKeysFromObject,
} from "../../../utils/object-utils.js";
import { UsersService } from "../../../core/users/users.service.js";
import { RolesService } from "../../../core/roles/roles.service.js";
import { handleDatabaseInsertException } from "../../../utils/pg-utils.js";
import { Branch } from "../branches/types/branches.types.js";
import { BranchesService } from "../branches/branches.service.js";
import { ModuleRef } from "@nestjs/core";
import {
  EntityId,
  FindAllEntitiesResponse,
  ServiceOptions,
} from "../../../shared/types/shared.types.js";
import { NewUser } from "../../../core/users/types/users.types.js";
import { AcademicSessionsService } from "../academic-sessions/academic-sessions.service.js";
import { ClassSectionsService } from "../classes/sections/class-sections.service.js";
import { SectionSubjectsService } from "../sections-subject-assignment/section-subjects.service.js";

// TODO: Add better argument types for staff update methods and remove assertions

@Injectable()
export class StaffService implements OnModuleInit {
  private readonly logger = new Logger(StaffService.name);

  private userService: UsersService;
  private classSectionsService: ClassSectionsService;
  private academicSessionsService: AcademicSessionsService;
  private sectionSubjectsService: SectionSubjectsService;

  public constructor(
    @InjectKysely() private readonly db: Kysely<DB>,
    private readonly branchesService: BranchesService,
    private readonly rolesService: RolesService,
    private readonly moduleRef: ModuleRef,
  ) {}

  public onModuleInit() {
    this.userService = this.moduleRef.get(UsersService, {
      strict: false,
    });
    this.classSectionsService = this.moduleRef.get(ClassSectionsService, {
      strict: false,
    });

    this.academicSessionsService = this.moduleRef.get(AcademicSessionsService, {
      strict: false,
    });

    this.sectionSubjectsService = this.moduleRef.get(SectionSubjectsService, {
      strict: false,
    });
  }

  /*
  |--------------------------------------------------------------------------
  | Find All Staff By Branch ID
  |--------------------------------------------------------------------------
  |
  | This public method retrieves all staff members associated with a specific
  | branch. It combines both institutional and support staff into a unified
  | result set using a UNION ALL query. The method supports pagination
  | through limit and offset parameters and returns staff sorted by
  | creation date.
  |
  */
  public async findAllByBranchId(
    branchId: string,
    queryOptions: ListStaffQueryOptions,
  ): Promise<FindAllEntitiesResponse<StaffProfile>> {
    try {
      const unifiedStaffQuery = this.selectInstitutionalStaffQuery()
        .where("staff.branchId", "=", branchId)
        .unionAll(
          this.selectSupportStaffQuery().where("staff.branchId", "=", branchId),
        );

      let query = this.db
        .with("unified_staff", _db => unifiedStaffQuery)
        .selectFrom("unified_staff")
        .selectAll()
        .limit(queryOptions.limit)
        .offset(queryOptions.offset)
        .orderBy("createdAt", "desc");

      if (queryOptions.type) {
        query = query.where("type", "=", queryOptions.type);
      }

      const [items, { total }] = await Promise.all([
        query.execute(),
        this.db
          .selectFrom("staff")
          .select(({ fn }) => [fn.count("staff.id").as("total")])
          .where("branchId", "=", branchId)
          .executeTakeFirstOrThrow(),
      ]);

      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find all staff", error);
      throw error;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Update Staff
  |--------------------------------------------------------------------------
  |
  | This method handles updating existing staff members in the system.
  | It determines the staff type (support or institutional) and delegates
  | the update operation to the appropriate specialized method. The process
  | includes validating the branch, finding the existing staff record,
  | and updating the relevant database tables within a transaction.
  |
  */
  public async update(
    id: Staff["id"],
    data: UpdateStaffPayload,
  ): Promise<EntityId> {
    await this.verifyBranchExists(data.branchId);

    const existingStaff = await this.findExistingStaffOrThrow(id);

    try {
      const staffInfo = extractKeysFromObject(data, ["salary", "designation"]);
      const userInfo = omitKeysFromObject(data, [
        "salary",
        "designation",
        "branchId",
      ]);

      if (existingStaff.department === "SUPPORT") {
        return await this.updateSupportStaff(
          id,
          existingStaff,
          staffInfo,
          userInfo,
        );
      }

      return await this.updateInstitutionalStaff(
        id,
        existingStaff,
        staffInfo,
        userInfo,
      );
    } catch (error: unknown) {
      this.logger.error("Failed to update staff", error);
      throw error;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Update Support Staff
  |--------------------------------------------------------------------------
  |
  | This private method handles updating support staff members.
  | It updates both the staff table for employment details (salary, designation)
  | and the supportStaffProfile table for personal information within a
  | single database transaction to ensure data consistency.
  |
  */
  private async updateSupportStaff(
    id: Staff["id"],
    existingStaff: Staff,
    staffInfo: Record<string, unknown>,
    staffAccountInfo: Record<string, unknown>,
  ): Promise<EntityId> {
    try {
      return await this.db.transaction().execute(async trx => {
        await trx
          .updateTable("supportStaffProfile")
          .set(staffAccountInfo)
          .where("id", "=", existingStaff.supportStaffProfileId)
          .returningAll()
          .executeTakeFirstOrThrow();

        return await trx
          .updateTable("staff")
          .set(staffInfo)
          .where("id", "=", id)
          .returning(["id"])
          .executeTakeFirstOrThrow();
      });
    } catch (error: unknown) {
      this.logger.error("Failed to update support staff", error);
      throw error;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Update Institutional Staff
  |--------------------------------------------------------------------------
  |
  | This private method handles updating institutional staff members
  | (teachers, administrators, accountants). It updates both the staff table
  | for employment details and the users table for personal information
  | within a single database transaction to ensure data consistency.
  |
  */
  private async updateInstitutionalStaff(
    id: Staff["id"],
    existingStaff: Staff,
    staffInfo: Record<string, unknown>,
    userInfo: Record<string, unknown>,
  ): Promise<EntityId> {
    try {
      return await this.db.transaction().execute(async trx => {
        const updatedStaff = await trx
          .updateTable("staff")
          .set(staffInfo)
          .where("id", "=", id)
          .returning(["id"])
          .executeTakeFirstOrThrow();

        await trx
          .updateTable("users")
          .set(userInfo)
          .where("id", "=", existingStaff.userId)
          .executeTakeFirstOrThrow();
        return updatedStaff;
      });
    } catch (error: unknown) {
      this.logger.error("Failed to update institutional staff", error);
      throw error;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Create new Staff
  |--------------------------------------------------------------------------
  |
  | This method handles the creation of new staff members for.
  | It supports creating both institutional staff
  | (teachers, administrators, accountants) and support staff. The process
  | includes creating a user account, assigning appropriate roles, generating
  | temporary credentials, and sending login information via email.
  |
  */
  public async create(
    createStaffPayload: CreateStaffPayload,
  ): Promise<EntityId> {
    await this.verifyBranchExists(createStaffPayload.branchId);

    try {
      if (this.isInstitutionalStaff(createStaffPayload)) {
        return await this.createInstitutionalStaff(createStaffPayload);
      }

      return await this.createSupportStaff(createStaffPayload);
    } catch (error: unknown) {
      handleDatabaseInsertException(error, {
        resource: "Staff",
        logger: this.logger,
      });
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Create Institutional Staff
  |--------------------------------------------------------------------------
  |
  | This private method handles the creation of institutional staff members
  | (teachers, administrators, accountants). It creates a user account with
  | the appropriate role, generates a random password, and creates a staff
  | record linked to the user. All operations are performed within a
  | transaction to ensure data consistency.
  |
  */
  private async createInstitutionalStaff(
    createStaffInput: CreateInstitutionStaffPayload,
  ): Promise<EntityId> {
    try {
      return await this.db.transaction().execute(async trx => {
        const staffPersonalInfo =
          this.extractPersonalProperties(createStaffInput);

        const staffRole = await this.rolesService.findByNameOrCreate(
          createStaffInput.type,
          trx,
        );

        const newUser = await this.userService.create(
          {
            ...staffPersonalInfo,
            roleId: staffRole.id,
          } as NewUser,
          trx,
        );

        const staffInfo = this.extractStaffProperties(createStaffInput);

        return await trx
          .insertInto("staff")
          .values({
            id: newUser.id,
            userId: newUser.id,
            ...staffInfo,
          })
          .returning(["id"])
          .executeTakeFirstOrThrow();
      });
    } catch (error: unknown) {
      this.logger.error(error);
      throw error;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Find Institutional Staff By ID
  |--------------------------------------------------------------------------
  |
  | This public method retrieves an institutional staff member by their ID.
  | It uses the selectInstitutionalStaffQuery to join the staff and users
  | tables, providing a complete profile of the staff member including
  | both personal and employment information.
  |
  */
  public async findInstitutionalStaffById(
    id: Staff["id"],
    options?: ServiceOptions,
  ): Promise<StaffProfile | undefined> {
    try {
      return await this.selectInstitutionalStaffQuery(options)
        .where("staff.userId", "=", id)
        .executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to find staff by id", error);
      throw error;
    }
  }

  public async getTeacherProfile(id: Staff["id"]) {
    try {
      const teacher = await this.findInstitutionalStaffById(id);

      if (!teacher) {
        throw new NotFoundException("Teacher not found");
      }

      const activeSession =
        await this.academicSessionsService.findActiveSessionByBranchId(
          teacher.branchId,
        );

      if (!activeSession) {
        throw new NotFoundException("Active session not found");
      }

      const teacherSections = await this.classSectionsService.find({
        classTeacherId: id,
        academicSessionId: activeSession.id,
      });

      const teacherSubjects = await this.sectionSubjectsService.find({
        subjectTeacherId: id,
        academicSessionId: activeSession.id,
      });

      return {
        ...teacher,
        classTeacherOf: teacherSections,
        subjectTeacherOf: teacherSubjects,
      };
    } catch (error: unknown) {
      this.logger.error("Failed to get teacher profile", error);
      throw error;
    }
  }

  public async verifyClassTeacherExists(
    classTeacherId: string,
    options?: ServiceOptions,
  ) {
    let existingClassTeacher: StaffProfile | undefined;
    try {
      existingClassTeacher = await this.findInstitutionalStaffById(
        classTeacherId,
        options,
      );
    } catch (error: unknown) {
      this.logger.error("Failed to verify class teacher exists", error);
      throw error;
    }

    if (!existingClassTeacher) {
      throw new NotFoundException("Class teacher not found");
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Find Existing Staff Or Throw
  |--------------------------------------------------------------------------
  |
  | This private method attempts to find a staff member by ID and throws
  | a NotFoundException if the staff member doesn't exist. It's used to
  | validate staff existence before performing operations that require
  | a valid staff record.
  |
  */
  private async findExistingStaffOrThrow(id: Staff["id"]): Promise<Staff> {
    let existingStaff: Staff | undefined;

    try {
      existingStaff = await this.db
        .selectFrom("staff")
        .where("id", "=", id)
        .selectAll()
        .executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to find existing staff", error);
      throw error;
    }

    if (!existingStaff) {
      throw new NotFoundException(`Staff with id: ${id} not found`);
    }
    return existingStaff;
  }

  /*
  |--------------------------------------------------------------------------
  | Create Support Staff
  |--------------------------------------------------------------------------
  |
  | This private method handles the creation of support staff members.
  | Unlike institutional staff, support staff don't have user accounts
  | but instead have profiles in the supportStaffProfile table. The method
  | creates both the profile and the staff record within a transaction.
  |
  */
  private async createSupportStaff(
    createStaffInput: CreateStaffPayload,
  ): Promise<EntityId> {
    try {
      return await this.db.transaction().execute(async trx => {
        const createSupportStaffProfileInput =
          this.extractPersonalProperties(createStaffInput);

        const createdSupportStaffProfile = await trx
          .insertInto("supportStaffProfile")
          .values(createSupportStaffProfileInput)
          .returning([
            "id",
            "name",
            "email",
            "phone",
            "address",
            "cnic",
            "photo",
            "gender",
          ])
          .executeTakeFirstOrThrow();

        const staffInfo = this.extractStaffProperties(createStaffInput);

        return await trx
          .insertInto("staff")
          .values({
            ...staffInfo,
            supportStaffProfileId: createdSupportStaffProfile.id,
          })
          .returning(["id"])
          .executeTakeFirstOrThrow();
      });
    } catch (error: unknown) {
      this.logger.error(error);
      throw error;
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Verify Branch Exists
  |--------------------------------------------------------------------------
  |
  | This private method verifies that a branch with the given ID exists
  | before proceeding with staff operations. It throws a NotFoundException
  | if the branch doesn't exist, ensuring that staff are only created or
  | updated for valid branches.
  |
  */
  private async verifyBranchExists(branchId: string) {
    let existingBranch: Branch | undefined;
    try {
      existingBranch = await this.branchesService.findById(branchId);
    } catch (error) {
      this.logger.error("Failed to verify branch exists", error);
      throw error;
    }

    if (!existingBranch) {
      throw new NotFoundException(`Branch with id: ${branchId} not found`);
    }
  }

  /*
  |--------------------------------------------------------------------------
  | Select Support Staff Query
  |--------------------------------------------------------------------------
  |
  | This private method builds a query to retrieve support staff data by
  | joining the supportStaffProfile and staff tables. It selects all
  | necessary fields from both tables and aliases them appropriately
  | to create a unified staff profile view.
  |
  */
  private selectSupportStaffQuery() {
    return this.db
      .selectFrom("supportStaffProfile")
      .innerJoin(
        "staff",
        "staff.supportStaffProfileId",
        "supportStaffProfile.id",
      )
      .select([
        "supportStaffProfile.name as name",
        "supportStaffProfile.email as email",
        "supportStaffProfile.phone as phone",
        "supportStaffProfile.address as address",
        "supportStaffProfile.gender as gender",
        "supportStaffProfile.photo as photo",
        "supportStaffProfile.cnic as cnic",
        "staff.id as id",
        "staff.department as department",
        "staff.designation as designation",
        "staff.salary as salary",
        "staff.createdAt as createdAt",
        "staff.branchId as branchId",
        "staff.type as type",
      ]);
  }

  /*
  |--------------------------------------------------------------------------
  | Select Institutional Staff Query
  |--------------------------------------------------------------------------
  |
  | This private method builds a query to retrieve institutional staff data
  | by joining the staff and users tables. It selects all necessary fields
  | from both tables and aliases them appropriately to create a unified
  | staff profile view for teachers, administrators, and accountants.
  |
  */
  private selectInstitutionalStaffQuery(options?: ServiceOptions) {
    const kyselyClient = options?.trx ?? this.db;
    return kyselyClient
      .selectFrom("staff")
      .innerJoin("users", "users.id", "staff.userId")
      .select([
        "users.name as name",
        "users.email as email",
        "users.phone as phone",
        "users.address as address",
        "users.gender as gender",
        "users.photo as photo",
        "users.cnic as cnic",
        "staff.id as id",
        "staff.department as department",
        "staff.designation as designation",
        "staff.salary as salary",
        "staff.createdAt as createdAt",
        "staff.branchId as branchId",
        "staff.type as type",
      ]);
  }

  /*
  |--------------------------------------------------------------------------
  | Extract User Properties
  |--------------------------------------------------------------------------
  |
  | This private helper method extracts personal information fields from
  | the staff input payload. These fields are used to create or update
  | user profiles for institutional staff or support staff profiles.
  |
  */
  private extractPersonalProperties<T extends CreateStaffPayload>(
    createStaffInput: T,
  ) {
    const userInfo = extractKeysFromObject(createStaffInput, [
      "name",
      "email",
      "phone",
      "address",
      "gender",
      "photo",
      "cnic",
    ]);
    if (this.isInstitutionalStaff(createStaffInput)) {
      return {
        ...userInfo,
        password: createStaffInput.password,
      };
    }
    return userInfo;
  }

  /*
  |--------------------------------------------------------------------------
  | Extract Staff Properties
  |--------------------------------------------------------------------------
  |
  | This private helper method extracts employment-related fields from
  | the staff input payload. These fields are used to create or update
  | staff records in the staff table, containing information about
  | the staff member's role, department, salary, and designation.
  |
  */
  private extractStaffProperties(createStaffInput: CreateStaffPayload) {
    return extractKeysFromObject(createStaffInput, [
      "salary",
      "department",
      "designation",
      "branchId",
      "type",
    ]);
  }

  private isInstitutionalStaff(
    createStaffInput: CreateStaffPayload | CreateInstitutionStaffPayload,
  ): createStaffInput is CreateInstitutionStaffPayload {
    return createStaffInput.department !== "SUPPORT";
  }
}
