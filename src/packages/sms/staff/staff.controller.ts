import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
  Res,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { StaffService } from "./staff.service.js";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import {
  CreateStaffParamDto,
  UpdateStaffParamDto,
} from "./dto/staff-param.dto.js";
import { CreateStaffDto, UpdateStaffDto } from "./dto/staff.dto.js";
import { ListAllEntitiesQueryDto } from "../../../shared/dto/query.dto.js";
import { ApiDocCreateStaff, ApiDocGetAllStaff } from "./docs/staff.docs.js";
import type { Response } from "express";

@Controller("sms/branches")
@ApiTags("Staff")
export class StaffController {
  public constructor(private readonly staffService: StaffService) {}

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Post("/:branchId/staff")
  @ApiDocCreateStaff()
  public async create(
    @Param() params: CreateStaffParamDto,
    @Body() createStaffDto: CreateStaffDto,
    @Res() res: Response,
  ) {
    const { id } = await this.staffService.create({
      ...createStaffDto,
      branchId: params.branchId,
    });
    res.setHeader("Location", id);
    res.status(HttpStatus.CREATED).send();
  }

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Get("/:branchId/staff")
  @ApiDocGetAllStaff()
  public async get(
    @Param() params: CreateStaffParamDto,
    @Query() query: ListAllEntitiesQueryDto,
  ) {
    return this.staffService.findAllByBranchId(params.branchId, query);
  }

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Patch("/:branchId/staff/:staffId")
  @HttpCode(HttpStatus.NO_CONTENT)
  public async update(
    @Param() param: UpdateStaffParamDto,
    @Body() updateStaffDto: UpdateStaffDto,
  ) {
    await this.staffService.update(param.staffId, {
      branchId: param.branchId,
      ...updateStaffDto,
    });
  }
}
