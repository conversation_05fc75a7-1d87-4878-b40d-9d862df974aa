import { ArgumentsHost, Catch, ExceptionFilter } from "@nestjs/common";
import { ZodValidationException } from "nestjs-zod";
import { Response } from "express";

@Catch(ZodValidationException)
export class ZodValidationExceptionFilter
  implements ExceptionFilter<ZodValidationException>
{
  public catch(exception: ZodValidationException, host: ArgumentsHost) {
    const error = exception.getZodError();
    const ctx = host.switchToHttp();
    const status = exception.getStatus();
    const response = ctx.getResponse<Response>();
    console.log(error);
    const errorsObject = error.issues.reduce(
      (acc, issue) => ({
        ...acc,
        [issue.path.join(".")]: issue.message,
      }),
      {},
    );

    const formErrors = error.flatten().formErrors;
    const formErrorMessage = formErrors[0]?.includes("Required")
      ? "Invalid request body. Expected JSON payload"
      : (formErrors[0] ?? "Unexpected error. Please check your request body.");

    if (formErrors.length > 0) {
      response.status(status).json({
        statusCode: status,
        message: formErrorMessage,
        data: null,
      });
      return;
    }

    response.status(status).json({
      statusCode: status,
      message: "Validation failed",
      data: errorsObject,
    });
  }
}
