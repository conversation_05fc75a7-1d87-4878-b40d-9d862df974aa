import { <PERSON>du<PERSON> } from "@nestjs/common";
import { StaffController } from "./staff.controller.js";
import { StaffService } from "./staff.service.js";
import { RolesModule } from "../../../core/roles/roles.module.js";
import { BranchesModule } from "../branches/branches.module.js";

@Module({
  imports: [RolesModule, BranchesModule],
  exports: [StaffService],
  controllers: [StaffController],
  providers: [StaffService],
})
export class StaffModule {}
