{"name": "ebridgeapi", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "type": "module", "scripts": {"build": "nest build", "format:write": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"test/**/*.ts\"", "generate:types": "kysely-codegen --out-file ./src/database/types.ts --camel-case", "migrate:latest": "kysely migrate latest", "migrate:down": "kysely migrate down", "migrate:rollback": "kysely migrate rollback --all", "migrate:up": "kysely migrate up", "migrate:make": "kysely migrate make", "seed:run": "kysely seed run", "seed:make": "kysely seed make", "seed:list": "kysely seed list", "start": "nest start", "start:dev": "npm run migrate:latest && npm run seed:run && nest start --tsc --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "vitest run", "test:watch": "vitest", "test:cov": "vitest run --coverage", "test:debug": "vitest --inspect-brk  --inspect --logHeapUsage --threads=false", "test:e2e": "vitest run --config ./test/end2end/vitest.config.e2e.ts", "ci": "npm run format:check && npm run lint && npm run test && npm run build", "prepare": "husky"}, "lint-staged": {"*.{ts,tsx}": ["prettier --write", "eslint  --no-warn-ignored   --max-warnings 0"], "*.md": ["prettier --write"]}, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^11.0.3", "bcrypt": "^5.1.1", "cookie-parser": "^1.4.7", "drizzle-orm": "^0.44.1", "drizzle-zod": "^0.8.2", "kysely": "^0.28.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "nestjs-zod": "^4.3.1", "pg": "^8.14.1", "postgres": "^3.4.7", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "zaplog": "^1.2.2", "zod": "^3.24.2"}, "overrides": {"@nestjs-modules/mailer": {"mjml": "^5.0.0-alpha.4"}}, "devDependencies": {"@darraghor/eslint-plugin-nestjs-typed": "^6.2.4", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/express": "^5.0.0", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.10.7", "@types/pg": "^8.11.12", "@types/supertest": "^6.0.2", "drizzle-kit": "^0.31.1", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^15.14.0", "husky": "^9.1.7", "kysely-ctl": "^0.12.2", "lint-staged": "^15.4.3", "prettier": "^3.4.2", "prisma": "^6.3.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^5.5.3", "typescript-eslint": "^8.20.0", "typescript-transform-paths": "^3.5.5", "unplugin-swc": "^1.5.1", "vitest": "^3.1.1", "vitest-mock-extended": "^3.1.0"}}