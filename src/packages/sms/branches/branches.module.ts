import { Module } from "@nestjs/common";
import { BranchesController } from "./branches.controller.js";
import { BranchesService } from "./branches.service.js";
import { InstitutesModule } from "../institutes/institutes.module.js";

@Module({
  controllers: [BranchesController],
  providers: [BranchesService],
  imports: [InstitutesModule],
  exports: [BranchesService],
})
export class BranchesModule {}
