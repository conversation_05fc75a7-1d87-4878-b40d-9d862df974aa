import { <PERSON>du<PERSON> } from "@nestjs/common";
import { <PERSON><PERSON><PERSON>roller } from "./diary.controller.js";
import { DiaryService } from "./diary.service.js";
import { ClassSectionsModule } from "../classes/sections/class-sections.module.js";
import { SubjectsModule } from "../subjects/subjects.module.js";

@Module({
  controllers: [DiaryController],
  imports: [ClassSectionsModule, SubjectsModule],
  providers: [DiaryService],
})
export class DiaryModule {}
