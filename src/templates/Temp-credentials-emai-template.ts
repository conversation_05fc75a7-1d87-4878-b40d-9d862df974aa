interface TempCredentialEmailArgs {
  email: string;
  password: string;
  loginUrl: string;
}

export const getTempCredentialEmailTemplate = ({
  email,
  password,
  loginUrl,
}: TempCredentialEmailArgs) => {
  return /*html*/ `<!DOCTYPE html>
  <html lang="en">
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Your Temporary Account Password</title>
      <style>
          body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333333;
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
          }
          .header {
              padding-bottom: 20px;
              border-bottom: 1px solid #eeeeee;
          }
          .logo {
              max-height: 60px;
              width: auto;
          }
          .content {
              padding: 30px 0;
          }
          .credentials {
              background-color: #f7f7f7;
              padding: 15px;
              border-radius: 5px;
              margin: 20px 0;
          }
          .credentials p {
              margin: 5px 0;
          }
          .button {
              display: inline-block;
              background-color: #4a6ee0;
              color: white;
              text-decoration: none;
              padding: 12px 25px;
              border-radius: 4px;
              margin-top: 15px;
              font-weight: bold;
              color: white,
          }
          .footer {
              margin-top: 30px;
              padding-top: 20px;
              border-top: 1px solid #eeeeee;
              font-size: 12px;
              color: #777777;
          }
      </style>
  </head>
  <body>
      <div class="content">
          <h2>Welcome to EBRIDGE</h2>
          
          <p>Hello,</p>
          
          <p>An administrator has created an account for you in our system. Please use the credentials below to log in for the first time:</p>
          
          <div class="credentials">
              <p><strong>Email:</strong> ${email}</p>
              <p><strong>Temporary Password:</strong> ${password}</p>
          </div>
          
          <p><strong>Important:</strong> For security reasons, you will be required to change your password on your first login.</p>
          
          <p>Your temporary password will expire in 48 hours. If you don't log in within this timeframe, please contact your administrator to request a new temporary password.</p>
          
          <a href="${loginUrl}" class="button">Log In Now</a>
          
          <p>If you have any questions or need assistance, please contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
          
          <p>Thank you,<br>
          The MB&CO Team</p>
      </div>
      
      <div class="footer">
          <p>This is an automated message. Please do not reply to this email.</p>
          <p>&copy; 2025 MB&CO. All rights reserved.</p>
          <p>If you did not request this account, please contact us immediately.</p>
      </div>
  </body>
  </html>`;
};
