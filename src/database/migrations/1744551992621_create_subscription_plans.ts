import { sql, type Kysely } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable("subscriptionPlan")
    .addColumn("id", "uuid", col =>
      col.primaryKey().defaultTo(sql`gen_random_uuid()`),
    )
    .addColumn("title", "text", col => col.notNull().unique())
    .addColumn("description", "text")
    .addColumn("price", "numeric(10, 2)", col => col.notNull().unique())
    .addColumn("setupCharges", "numeric(10, 2)", col => col.notNull())
    .addColumn("branches", "integer", col => col.notNull())
    .addColumn("students", "integer", col => col.notNull())
    .addColumn("features", sql`TEXT[]`, col => col.notNull())
    .addColumn("createdAt", "timestamptz", col =>
      col.notNull().defaultTo(sql`(NOW())`),
    )
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("subscriptionPlan").execute();
}
