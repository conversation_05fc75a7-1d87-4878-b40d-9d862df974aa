import { Controller, Get } from "@nestjs/common";
import { AppService } from "./app.service.js";
import { ConfigService } from "@nestjs/config";
import { ApiPingResponse } from "./swagger/app.swagger.js";
import { ApiTags } from "@nestjs/swagger";
import { Public } from "../shared/decorators/public.decorator.js";

@ApiTags("Health Check")
@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly configService: ConfigService,
  ) {}

  @ApiPingResponse()
  @Public()
  @Get("/status")
  public async getHello() {
    return await this.appService.checkStatus();
  }
}
