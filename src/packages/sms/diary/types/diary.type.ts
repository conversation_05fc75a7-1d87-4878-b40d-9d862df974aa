import { Insertable, Selectable, Updateable } from "kysely";
import { DiaryTable } from "../../../../database/types.js";
import {
  diaryResponseSchema,
  listDiariesQuerySchema,
} from "../dto/diary.dto.js";
import { z } from "zod";

export type Diary = Selectable<DiaryTable>;
export type NewDiary = Insertable<DiaryTable>;
export type DiaryUpdate = Updateable<DiaryTable>;

// Response types
export type DiaryResponse = z.infer<typeof diaryResponseSchema>;

// Query types
export type ListDiariesQueryOptions = z.infer<typeof listDiariesQuerySchema>;
