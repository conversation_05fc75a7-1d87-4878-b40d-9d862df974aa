import { Body, Controller, Get, Param, Post, Query } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { BranchesService } from "./branches.service.js";
import { CreateBranchDto } from "./dto/branches.dto.js";
import { CreateBranchParamsIdDto } from "./dto/params/branches-param.dto.js";
import {
  ApiDocCreateBranch,
  ApiDocGetAllBranches,
} from "./docs/branches.docs.js";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import { ListAllEntitiesQueryDto } from "../../../shared/dto/query.dto.js";

@Controller("sms/institutes")
@ApiTags("Institute Branches")
export class BranchesController {
  constructor(private readonly branchesService: BranchesService) {}

  @Roles(["INSTITUTE_OWNER", "PLATFORM_ADMIN"])
  @Get(":instituteId/branches")
  @ApiDocGetAllBranches()
  public async getAll(
    @Param() { instituteId }: CreateBranchParamsIdDto,
    @Query() query: ListAllEntitiesQueryDto,
  ) {
    return await this.branchesService.findAll(instituteId, query);
  }

  @Roles(["INSTITUTE_OWNER", "PLATFORM_ADMIN"])
  @Post(":instituteId/branches")
  @ApiDocCreateBranch()
  public async create(
    @Param() { instituteId }: CreateBranchParamsIdDto,
    @Body() createBranchDto: CreateBranchDto,
  ) {
    return await this.branchesService.create({
      ...createBranchDto,
      instituteId,
    });
  }
}
