import { type Kysely, sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable("academicSession")
    .addColumn("id", "uuid", col =>
      col.primaryKey().defaultTo(sql`gen_random_uuid()`),
    )
    .addColumn("name", "text", col => col.notNull())
    .addColumn("startDate", "date", col => col.notNull())
    .addColumn("endDate", "date", col => col.notNull())
    .addColumn("branchId", "uuid", col => col.notNull())
    .addColumn("isActive", "boolean", col => col.notNull().defaultTo(false))
    .addColumn("createdAt", "timestamptz", col =>
      col.notNull().defaultTo(sql`(NOW())`),
    )
    .addCheckConstraint(
      "start_date_before_end_date",
      sql`start_date < end_date`,
    )
    .addUniqueConstraint("academic_session_date_unique", [
      "startDate",
      "endDate",
    ])
    .addForeignKeyConstraint(
      "academic_session_branchId_fkey",
      ["branchId"],
      "branch",
      ["id"],
    )
    .execute();

  await db.schema
    .createIndex("academic_session_isActive_index")
    .on("academicSession")
    .columns(["branchId", "isActive"])
    .where("isActive", "=", true)
    .unique()
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("academicSession").execute();
}
