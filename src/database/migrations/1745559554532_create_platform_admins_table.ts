import { sql, type <PERSON>ys<PERSON> } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  await db.schema
    .createTable("platformAdmin")
    .addColumn("userId", "uuid", col => col.notNull().primaryKey())
    .addColumn("createdAt", "timestamptz", col =>
      col.notNull().defaultTo(sql`(NOW())`),
    )
    .addForeignKeyConstraint(
      "admin_userId_fkey",
      ["userId"],
      "users",
      ["id"],
      cb => cb.onUpdate("cascade").onDelete("restrict"),
    )
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("platformAdmin").execute();
}
