import { ApiOperation, ApiResponse } from "@nestjs/swagger";
import { applyDecorators } from "@nestjs/common";
import { commonApiResponseOptions } from "../../../docs/shared/api-responses.js";

/**
 * Swagger documentation for the getCurrentUserProfile endpoint
 * This endpoint returns different profile types based on the user's role
 * Uses oneOf to document the different possible response schemas
 */
export function ApiDocGetCurrentUserProfile() {
  return applyDecorators(
    ApiOperation({
      summary: "Get Current User Profile",
      description: `
        Retrieves the current authenticated user's profile information.
        The response structure varies based on the user's role:

        **Role-based Response Types:**
        - **General Users**: Basic user profile with standard fields
        - **Institute Owners**: User profile extended with setup completion status
        - **Teachers**: Staff profile extended with teaching assignments and class information

        **Authentication Required:** Bearer token must be provided in the Authorization header.

        **Response Variations:**
        - Institute owners receive additional \`hasCompletedSetup\` field
        - Teachers receive additional \`classTeacherOf\` and \`subjectTeacherOf\` arrays
        - Other users receive the base user profile structure

        **Teaching Assignment Details (Teachers only):**
        - \`classTeacherOf\`: Array of class sections where the teacher is the class teacher
        - \`subjectTeacherOf\`: Array of subject teaching assignments with class, section, and subject details
      `,
    }),
    ApiResponse({
      status: 200,
      description: "Successfully retrieved user profile",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              statusCode: { type: "number", example: 200 },
              message: {
                type: "string",
                example: "Profile retrieved successfully",
              },
              data: {
                oneOf: [
                  {
                    $ref: "#/components/schemas/BaseUserProfileResponseDto",
                    description: "Base user profile for general users",
                  },
                  {
                    $ref: "#/components/schemas/InstituteOwnerProfileResponseDto",
                    description: "Institute owner profile with setup status",
                  },
                  {
                    $ref: "#/components/schemas/TeacherProfileResponseDto",
                    description: "Teacher profile with teaching assignments",
                  },
                ],
                discriminator: {
                  propertyName: "role",
                  mapping: {
                    "3000":
                      "#/components/schemas/InstituteOwnerProfileResponseDto",
                    "4000": "#/components/schemas/TeacherProfileResponseDto",
                  },
                },
              },
            },
          },
          examples: {
            "institute-owner": {
              summary: "Institute Owner Profile",
              description: "Example response for an institute owner user",
              value: {
                statusCode: 200,
                message: "Profile retrieved successfully",
                data: {
                  id: "123e4567-e89b-12d3-a456-426614174000",
                  name: "John Doe",
                  email: "<EMAIL>",
                  phone: "+1234567890",
                  address: "123 Main St, City, Country",
                  gender: "MALE",
                  cnic: "12345-6789012-3",
                  photo: "https://example.com/photo.jpg",
                  role: 3000,
                  isActive: true,
                  isPasswordTemporary: false,
                  createdAt: "2024-01-15T10:30:00Z",
                  hasCompletedSetup: true,
                },
              },
            },
            teacher: {
              summary: "Teacher Profile",
              description: "Example response for a teacher user",
              value: {
                statusCode: 200,
                message: "Profile retrieved successfully",
                data: {
                  id: "456e7890-e89b-12d3-a456-426614174001",
                  name: "Jane Smith",
                  email: "<EMAIL>",
                  phone: "+1234567891",
                  address: "456 School Ave, City, Country",
                  gender: "FEMALE",
                  cnic: "12345-6789012-4",
                  photo: "https://example.com/teacher-photo.jpg",
                  designation: "Mathematics Teacher",
                  department: "ACADEMIC",
                  type: "TEACHER",
                  salary: 50000,
                  createdAt: "2024-01-15T10:30:00Z",
                  branchId: "789e0123-e89b-12d3-a456-426614174002",
                  classTeacherOf: [
                    {
                      id: "section-123",
                      name: "Section A",
                      totalStudents: 30,
                      class: {
                        id: "class-456",
                        name: "Grade 10",
                      },
                      createdAt: "2024-01-15T10:30:00Z",
                    },
                  ],
                  subjectTeacherOf: [
                    {
                      id: "assignment-789",
                      academicSessionId: "session-2024",
                      class: {
                        id: "class-456",
                        name: "Grade 10",
                      },
                      section: {
                        id: "section-123",
                        name: "Section A",
                        totalStudents: 30,
                      },
                      subject: {
                        id: "subject-math",
                        name: "Mathematics",
                      },

                      createdAt: "2024-01-15T10:30:00Z",
                    },
                  ],
                },
              },
            },
            "general-user": {
              summary: "General User Profile",
              description: "Example response for a general user",
              value: {
                statusCode: 200,
                message: "Profile retrieved successfully",
                data: {
                  id: "789e0123-e89b-12d3-a456-426614174003",
                  name: "Bob Johnson",
                  email: "<EMAIL>",
                  phone: "+1234567892",
                  address: "789 User St, City, Country",
                  gender: "MALE",
                  cnic: "12345-6789012-5",
                  photo: null,
                  role: 5000,
                  isActive: true,
                  isPasswordTemporary: false,
                  createdAt: "2024-01-15T10:30:00Z",
                },
              },
            },
          },
        },
      },
    }),
    ApiResponse(commonApiResponseOptions.NOT_FOUND),
    ApiResponse(commonApiResponseOptions.FORBIDDEN),
    ApiResponse(commonApiResponseOptions.INTERNAL_SERVER_ERROR),
  );
}
