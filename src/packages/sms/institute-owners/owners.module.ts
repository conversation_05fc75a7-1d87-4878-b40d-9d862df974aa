import { Module } from "@nestjs/common";
import { OwnersController } from "./owners.controller.js";
import { OwnersService } from "./owners.service.js";
import { RolesModule } from "../../../core/roles/roles.module.js";
import { SubscriptionPlansModule } from "../../platform/subscriptions-plans/subscription-plans.module.js";

@Module({
  imports: [RolesModule, SubscriptionPlansModule],
  controllers: [OwnersController],
  providers: [OwnersService],
  exports: [OwnersService],
})
export class InstituteOwnersModule {}
