import { Insertable, Selectable, Updateable } from "kysely";
import { UsersTable } from "../../../database/types.js";
import { createUserSchema, userProfileSchema } from "../dto/user.dto.js";
import { z } from "zod";

export type User = Selectable<UsersTable>;
export type NewUser = Insertable<UsersTable>;
export type UserUpdate = Updateable<UsersTable>;

// Base user profile
export type UserProfile = z.infer<typeof userProfileSchema>;

// UserProfile with temporary password field
export type UserWithTempPassword = UserProfile & {
  tempPassword: string;
};

// Input payload for creating user
export type CreateUserPayload = z.infer<typeof createUserSchema>;
