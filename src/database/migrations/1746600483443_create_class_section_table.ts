import type { Kysely } from "kysely";
import { sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable("classSection")
    .addColumn("id", "uuid", col =>
      col.primaryKey().defaultTo(sql`gen_random_uuid()`),
    )
    .addColumn("name", "text", col => col.notNull())
    .addColumn("classId", "uuid", col =>
      col
        .notNull()
        .references("class.id")
        .onDelete("restrict")
        .onUpdate("cascade"),
    )
    .addColumn("classTeacherId", "uuid", col =>
      col
        .notNull()
        .references("staff.id")
        .onDelete("restrict")
        .onUpdate("cascade"),
    )
    .addColumn("isActive", "boolean", col => col.notNull().defaultTo(true))
    .addColumn("createdAt", "timestamptz", col =>
      col.notNull().defaultTo(sql`now()`),
    )
    .addUniqueConstraint("classSection_name_classId_unique", [
      "name",
      "classId",
    ])
    .execute();

  await db.schema
    .createIndex("idx_classSection_classId")
    .on("classSection")
    .columns(["classId"])
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("classSection").execute();
}
