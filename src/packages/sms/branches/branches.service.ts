import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { InjectKysely } from "../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { Kysely } from "kysely";
import { DB } from "../../../database/types.js";
import { InstitutesService } from "../institutes/institutes.service.js";
import { ListAllEntitiesQueryOptions } from "../../../shared/types/shared.types.js";
import { Branch, NewBranch } from "./types/branches.types.js";
import { handleDatabaseInsertException } from "../../../utils/pg-utils.js";
import { InstituteResponse } from "../institutes/types/institute.types.js";

@Injectable()
export class BranchesService {
  public constructor(
    @InjectKysely() private readonly db: Kysely<DB>,
    private readonly institutesService: InstitutesService,
  ) {}

  private readonly logger = new Logger(BranchesService.name);

  public async findAll(
    instituteId: Branch["instituteId"],
    { limit, offset }: ListAllEntitiesQueryOptions,
  ): Promise<Branch[]> {
    try {
      return await this.findBranchQuery({ instituteId })
        .limit(limit)
        .offset(offset)
        .execute();
    } catch (error: unknown) {
      this.logger.error("Failed to find all branches", error);
      throw error;
    }
  }

  public async create(data: NewBranch): Promise<Branch> {
    await this.verifyBranchExists(data.instituteId);

    try {
      return await this.db
        .insertInto("branch")
        .values(data)
        .returningAll()
        .executeTakeFirstOrThrow();
    } catch (error) {
      handleDatabaseInsertException(error, {
        resource: "branch",
        logger: this.logger,
      });
    }
  }

  public async findById(branchId: Branch["id"]): Promise<Branch | undefined> {
    try {
      return await this.findBranchQuery({
        id: branchId,
      }).executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to find unique branch", error);
      throw error;
    }
  }

  public async findBranchByName(
    branchName: Branch["name"],
  ): Promise<Branch | undefined> {
    try {
      return await this.findBranchQuery({
        name: branchName,
      }).executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to find unique branch", error);
      throw error;
    }
  }

  private async verifyBranchExists(instituteId: Branch["instituteId"]) {
    let institute: InstituteResponse | undefined;
    try {
      institute = await this.institutesService.findById(instituteId);
    } catch (error: unknown) {
      this.logger.error("Failed to check if institute exists", error);
      throw error;
    }

    if (!institute) {
      throw new NotFoundException(
        `Institute with id: '${instituteId}' does not exist`,
      );
    }
  }

  private findBranchQuery(
    criteria?: Partial<Pick<Branch, "name" | "id" | "instituteId">>,
  ) {
    let query = this.db
      .selectFrom("branch")
      .select([
        "branch.id",
        "branch.name",
        "branch.address",
        "branch.email",
        "branch.phone",
        "branch.isMain",
        "branch.isActive",
        "branch.createdAt",
        "branch.instituteId",
      ]);

    if (criteria?.name) {
      query = query.where("branch.name", "=", criteria.name);
    }

    if (criteria?.id) {
      query = query.where("branch.id", "=", criteria.id);
    }

    if (criteria?.instituteId) {
      query = query.where("branch.instituteId", "=", criteria.instituteId);
    }

    return query;
  }
}
