import { Injectable, Logger } from "@nestjs/common";
import { MailerService } from "@nestjs-modules/mailer";
import { getTempCredentialEmailTemplate } from "../../templates/Temp-credentials-emai-template.js";
import { EnvService } from "./env/env.service.js";

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  public constructor(
    private readonly mailService: MailerService,
    private readonly envService: EnvService,
  ) {}

  public async sendEmailWithTempCredentials(credentials: {
    email: string;
    password: string;
  }) {
    try {
      const response = (await this.mailService.sendMail({
        from: 'Demo Mailtrap" <<EMAIL>>',
        to: "<EMAIL>",
        subject: "Login",
        html: getTempCredentialEmailTemplate({
          email: credentials.email,
          password: credentials.password,
          loginUrl: this.envService.get("LOGIN_URL"),
        }),
      })) as unknown;
      this.logger.log(
        `Email sent to ${credentials.email} with temporary credentials`,
        response,
      );
    } catch (error: unknown) {
      this.logger.error("Failed to send set-password email", error);
    }
  }
}
