import { type Kysely, sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  try {
    await db.schema
      .createType("guardianRelation")
      .asEnum(["FATHER", "MOTHER", "GUARDIAN"])
      .execute();
  } catch (error: unknown) {
    console.error("Failed to create guardian relation type", error);
    throw error;
  }

  try {
    await db.schema
      .createTable("guardian")
      .addColumn("userId", "uuid", col =>
        col
          .primaryKey()
          .references("users.id")
          .onDelete("restrict")
          .onUpdate("cascade"),
      )
      .addColumn("relation", sql`guardian_relation`, col => col.notNull())
      .addColumn("createdAt", "timestamptz", col =>
        col.notNull().defaultTo(sql`NOW()`),
      )

      .execute();
  } catch (error: unknown) {
    console.error("Failed to create guardian table", error);
    throw error;
  }
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  try {
    await db.schema.dropTable("guardian").execute();
  } catch (error: unknown) {
    console.error("Failed to drop guardian table", error);
    throw error;
  }

  try {
    await db.schema.dropType("guardianRelation").execute();
  } catch (error: unknown) {
    console.error("Failed to drop guardian relation type", error);
    throw error;
  }
}
