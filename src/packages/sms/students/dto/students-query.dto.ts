import { createZodDto } from "nestjs-zod";
import {
  getUuidSchema,
  listAllEntitiesQuerySchema,
} from "../../../../shared/schema/zod-common.schema.js";
import {
  enrollmentStatusSchema,
  enrollmentTypeSchema,
} from "../enrollments/dto/enrollments.dto.js";

const listStudentsQuerySchema = listAllEntitiesQuerySchema.extend({
  classId: getUuidSchema("Class ID").optional(),
  sectionId: getUuidSchema("Section ID").optional(),
  enrollmentStatus: enrollmentStatusSchema.optional(),
  enrollmentType: enrollmentTypeSchema.optional(),
});

export class ListStudentsQueryDto extends createZodDto(
  listStudentsQuerySchema,
) {}
