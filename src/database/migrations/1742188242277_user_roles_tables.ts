import { Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  /* ------ Gender Type --------- */
  await db.schema
    .createType("gender")
    .asEnum(["MALE", "FEMAL<PERSON>", "OTHER"])
    .execute();

  /* ------ User Role Type --------- */
  await db.schema
    .createType("roleName")
    .asEnum([
      "BRANCH_ADMIN",
      "ACCOUNTANT",
      "PLATFORM_ADMIN",
      "INSTITUTE_OWNER",
      "TEACHER",
      "SUPPORT_STAFF",
      "STUDENT",
      "GUARDIAN",
    ])
    .execute();

  /* ------ Role Table --------- */
  await db.schema
    .createTable("role")
    .addColumn("id", "uuid", col =>
      col.primaryKey().defaultTo(sql`gen_random_uuid()`),
    )
    .addColumn("name", sql`role_name`, col => col.notNull().unique())
    .addColumn("code", "integer", col => col.notNull().unique())
    .addColumn("description", "text", col => col.notNull())
    .addColumn("createdAt", "timestamptz", col =>
      col.notNull().defaultTo(sql`(NOW())`),
    )
    .execute();

  /* ------ User Table --------- */
  await db.schema
    .createTable("users")
    .addColumn("id", "uuid", col =>
      col.primaryKey().defaultTo(sql`gen_random_uuid()`),
    )
    .addColumn("name", "text", col => col.notNull())
    .addColumn("email", "text", col => col.notNull().unique())
    .addColumn("phone", "text", col => col.notNull().unique())
    .addColumn("address", "text", col => col.notNull())
    .addColumn("gender", sql`gender`, col => col.notNull())
    .addColumn("photo", "text")
    .addColumn("roleId", "uuid", col =>
      col
        .notNull()
        .references("role.id")
        .onDelete("restrict")
        .onUpdate("cascade"),
    )
    .addColumn("cnic", "text", col => col.notNull().unique())
    .addColumn("isActive", "boolean", col => col.notNull().defaultTo(true))
    .addColumn("password", "text", col => col.notNull())
    .addColumn("isPasswordTemporary", "boolean", col =>
      col.notNull().defaultTo(false),
    )
    .addColumn("createdAt", "timestamptz", col =>
      col.notNull().defaultTo(sql`(NOW())`),
    )
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("users").execute();
  await db.schema.dropTable("role").execute();
  await db.schema.dropType("gender").execute();
  await db.schema.dropType("roleName").execute();
}
