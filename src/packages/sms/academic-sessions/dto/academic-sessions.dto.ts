import { z } from "zod";
import { dateSchema } from "../../../../shared/schema/zod-common.schema.js";
import { createZodDto } from "nestjs-zod";

const academicSessionBaseSchema = z
  .object({
    name: z.string().max(100, { message: "Name cannot exceed 100 characters" }),
    startDate: dateSchema,
    endDate: dateSchema,
    isActive: z.boolean().default(true),
  })
  .refine(data => data.startDate < data.endDate, {
    message: "Start date must be before end date",
  });

export class CreateAcademicSessionDto extends createZodDto(
  academicSessionBaseSchema,
) {}
