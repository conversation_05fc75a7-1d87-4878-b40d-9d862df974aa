import { Insertable, Selectable, Updateable } from "kysely";
import { ClassSectionTable } from "../../../../../database/types.js";
import { z } from "zod";
import { classSectionResponseSchema } from "../dto/class-sections.dto.js";

export type ClassSection = Selectable<ClassSectionTable>;
export type NewClassSection = Insertable<ClassSectionTable>;
export type ClassSectionUpdate = Updateable<ClassSectionTable>;

export type ClassSectionResponse = z.infer<typeof classSectionResponseSchema>;
