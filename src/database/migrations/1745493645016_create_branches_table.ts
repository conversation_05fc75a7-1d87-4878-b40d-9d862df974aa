import { sql, type Kysely } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable("branch")
    .addColumn("id", "uuid", col =>
      col.primaryKey().defaultTo(sql`gen_random_uuid()`),
    )
    .addColumn("email", "text", col => col.notNull())
    .addColumn("name", "text", col => col.notNull().unique())
    .addColumn("address", "text", col => col.notNull())
    .addColumn("phone", "text", col => col.notNull())
    .addColumn("isMain", "boolean", col => col.notNull().defaultTo(false))
    .addColumn("isActive", "boolean", col => col.notNull().defaultTo(true))
    .addColumn("instituteId", "uuid", col => col.notNull())
    .addColumn("createdAt", "timestamptz", col =>
      col.notNull().defaultTo(sql`(NOW())`),
    )
    .addForeignKeyConstraint(
      "branch_instituteId_fkey",
      ["instituteId"],
      "institute",
      ["id"],
    )
    .execute();

  await db.schema
    .createIndex("branch_isMain_unique_index")
    .on("branch")
    .columns(["isMain", "instituteId"])
    .where("isMain", "=", true)
    .unique()
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("branch").execute();
}
