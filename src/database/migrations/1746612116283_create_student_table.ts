import { type Kysely, sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createType("religion")
    .asEnum([
      "ISLAM",
      "CHRISTIANITY",
      "HINDUISM",
      "BUDDHISM",
      "SIKHISM",
      "JUDAISM",
      "OTHER",
    ])
    .execute();

  await db.schema
    .createTable("student")
    .addColumn("id", "uuid", col =>
      col.primaryKey().defaultTo(sql`gen_random_uuid()`),
    )
    .addColumn("name", "text", col => col.notNull())
    .addColumn("registrationNumber", "text")
    .addColumn("email", "text", col => col.unique())
    .addColumn("fatherName", "text", col => col.notNull())
    .addColumn("address", "text", col => col.notNull())
    .addColumn("gender", sql`gender`, col => col.notNull())
    .addColumn("photo", "text")
    .addColumn("religion", sql`religion`, col => col.notNull())
    .addColumn("monthlyFee", "numeric(10, 2)", col => col.notNull())
    .addColumn("admissionDate", "date", col => col.notNull())
    .addColumn("dateOfBirth", "date", col => col.notNull())
    .addColumn("previousSchool", "text")
    .addColumn("classSectionId", "uuid", col =>
      col
        .notNull()
        .references("classSection.id")
        .onDelete("restrict")
        .onUpdate("cascade"),
    )
    .addColumn("guardianId", "uuid", col =>
      col
        .notNull()
        .references("guardian.userId")
        .onDelete("restrict")
        .onUpdate("cascade"),
    )
    .addColumn("createdAt", "timestamptz", col =>
      col.notNull().defaultTo(sql`NOW()`),
    )
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  try {
    await db.schema.dropTable("student").execute();
  } catch (error: unknown) {
    console.error("Failed to drop student table", error);
    throw error;
  }
}
