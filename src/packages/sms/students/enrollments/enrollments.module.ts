import { Module } from "@nestjs/common";
import { EnrollmentService } from "./enrollments.service.js";
import { EnrollmentsController } from "./enrollments.controller.js";
import { AcademicSessionsModule } from "../../academic-sessions/academic-sessions.module.js";
import { ClassSectionsModule } from "../../classes/sections/class-sections.module.js";

@Module({
  imports: [AcademicSessionsModule, ClassSectionsModule],
  controllers: [EnrollmentsController],
  providers: [EnrollmentService],
  exports: [EnrollmentService],
})
export class EnrollmentsModule {}
