{
  "compilerOptions": {
    "target": "ES2022",
    "useDefineForClassFields": true,
    "lib": ["ESNext"],
    "module": "NodeNext",
    "moduleResolution": "nodenext",
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowJs": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "esModuleInterop": true,
    "types": ["vitest/globals"],
    "declaration": true,
    "removeComments": true,
    "allowSyntheticDefaultImports": true,
    "skipLibCheck": true,
    "outDir": "dist",
    /* Linting */
    "strict": true,
    "strictPropertyInitialization": false,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "forceConsistentCasingInFileNames": true,
    "strictBindCallApply": true
  },
  "watchOptions": {
    "watchFile": "dynamicpriorityPolling",
    "watchDirectory": "dynamicPriorityPolling",
    "fallbackPolling": "dynamicPriority",
    "synchronousWatchDirectory": true,
    "excludeDirectories": ["**/node_modules", "dist"]
  },
  "include": ["src", "test", "prisma/seed.ts"],
  "exclude": ["node_modules"]
}
