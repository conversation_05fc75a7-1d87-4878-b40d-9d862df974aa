import { sql, type Kysely } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable("subscription")
    .addColumn("id", "uuid", col =>
      col.primaryKey().defaultTo(sql`gen_random_uuid()`),
    )
    .addColumn("planId", "uuid", col => col.notNull())
    .addColumn("ownerId", "uuid", col => col.notNull().unique())
    .addColumn("status", "varchar(20)", col =>
      col
        .notNull()
        .check(sql`status IN ('active', 'cancelled', 'unpaid', 'expired')`),
    )
    .addColumn("startDate", "timestamptz", col =>
      col.notNull().defaultTo(sql`(NOW())`),
    )
    .addColumn("paymentCycle", "varchar(20)", col =>
      col.notNull().check(sql`payment_cycle IN ('monthly', 'yearly')`),
    )
    .addColumn("gracePeriodDays", "integer", col => col.notNull().defaultTo(5))
    .addColumn("endDate", "timestamptz", col => col.notNull())

    .addColumn("nextPaymentDate", "timestamptz", col => col.notNull())

    .addColumn("lastPaymentDate", "timestamptz", col =>
      col.notNull().defaultTo(sql`(NOW())`),
    )
    .addColumn("cancellationDate", "timestamptz")
    .addColumn("createdAt", "timestamptz", col =>
      col.notNull().defaultTo(sql`(NOW())`),
    )
    .addForeignKeyConstraint(
      "subscription_planId_fkey",
      ["planId"],
      "subscriptionPlan",
      ["id"],
    )
    .addForeignKeyConstraint(
      "subscription_userId_fkey",
      ["ownerId"],
      "instituteOwner",
      ["userId"],
    )
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("subscription").execute();
}
