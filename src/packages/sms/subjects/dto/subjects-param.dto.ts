import { createZodDto } from "nestjs-zod";
import { getUuidSchema } from "../../../../shared/schema/zod-common.schema.js";
import { academicSessionIdParamSchema } from "../../../../shared/dto/param.dto.js";

const sessionSubjectParamSchema = academicSessionIdParamSchema.extend({
  subjectId: getUuidSchema("Subject ID"),
});
export class SessionSubjectParamDto extends createZodDto(
  sessionSubjectParamSchema,
) {}
