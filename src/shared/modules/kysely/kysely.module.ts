import { DynamicModule, Module, Provider } from "@nestjs/common";
import { KyselyConfig } from "kysely";
import {
  createKyselyClient,
  createKyselyProviders,
} from "./providers/kysely.providers.js";
import { KyselyModuleAsyncOptions } from "./interfaces/kysely.interface.js";
import {
  KYSELY_MODULE_CONNECTION_TOKEN,
  KYSELY_MODULE_OPTIONS_TOKEN,
} from "./constants/kysely.constants.js";
import { KyselyService } from "./services/kysely.service.js";

@Module({})
export class KyselyModule {
  public static forRoot(config: KyselyConfig): DynamicModule {
    const providers = createKyselyProviders(config);
    return {
      global: true,
      module: KyselyModule,
      providers: [...providers, KyselyService],
      exports: providers,
    };
  }

  public static forRootAsync(options: KyselyModuleAsyncOptions): DynamicModule {
    const provider: Provider = {
      inject: [KYSELY_MODULE_OPTIONS_TOKEN, KyselyService],
      provide: KYSELY_MODULE_CONNECTION_TOKEN,
      useFactory: (config: KyselyConfig, service: KyselyService) => {
        return createKyselyClient(config, service);
      },
    };

    return {
      global: true,
      exports: [provider],
      module: KyselyModule,
      providers: [
        {
          inject: options.inject,
          provide: KYSELY_MODULE_OPTIONS_TOKEN,
          useFactory: options.useFactory,
        },
        KyselyService,
        provider,
      ],
      imports: options.imports,
    };
  }
}
