import { createZodDto } from "nestjs-zod";
import { getApiResponseSchema } from "../../../shared/schema/api-response.schema.js";
import { userProfileSchema } from "./user.dto.js";
import { ownerResponseSchema } from "../../../packages/sms/institute-owners/dto/owners.dto.js";
import { teacherProfileSchema } from "../../../packages/sms/staff/dto/staff.dto.js";

/**
 * Base user profile response schema
 * Used for general users without specific role extensions
 */
const baseUserProfileResponseSchema = userProfileSchema;

/**
 * Institute owner profile response schema
 * Used for institute owner users with setup completion status
 */
const instituteOwnerProfileResponseSchema = ownerResponseSchema;

/**
 * Teacher profile response schema
 * Used for teacher users with teaching assignments and class information
 */
const teacherProfileResponseSchema = teacherProfileSchema;

/**
 * API response wrappers for different user profile types
 * Each response type includes status code, message, and the specific profile data
 */
export const baseUserProfileApiResponseSchema = getApiResponseSchema(
  baseUserProfileResponseSchema,
);

export const instituteOwnerProfileApiResponseSchema = getApiResponseSchema(
  instituteOwnerProfileResponseSchema,
);

export const teacherProfileApiResponseSchema = getApiResponseSchema(
  teacherProfileResponseSchema,
);

// Export DTO classes for Swagger documentation
export class BaseUserProfileResponseDto extends createZodDto(
  baseUserProfileResponseSchema,
) {}

export class InstituteOwnerProfileResponseDto extends createZodDto(
  instituteOwnerProfileResponseSchema,
) {}

export class TeacherProfileResponseDto extends createZodDto(
  teacherProfileResponseSchema,
) {}

// API Response DTOs for Swagger documentation
export class BaseUserProfileApiResponseDto extends createZodDto(
  baseUserProfileApiResponseSchema,
) {}

export class InstituteOwnerProfileApiResponseDto extends createZodDto(
  instituteOwnerProfileApiResponseSchema,
) {}

export class TeacherProfileApiResponseDto extends createZodDto(
  teacherProfileApiResponseSchema,
) {}
