import { Body, Controller, Get, Param, Post, Query } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { OwnersService } from "./owners.service.js";

import { ApiDocCreateOwner } from "./docs/owners.docs.js";
import { CreateOwnerDto } from "./dto/owners.dto.js";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import { ListAllEntitiesQueryDto } from "../../../shared/dto/query.dto.js";
import { IdParamDto } from "../../../shared/dto/param.dto.js";

@Controller("sms/institute-owners")
@ApiTags("Institute Owners")
export class OwnersController {
  public constructor(private readonly ownersService: OwnersService) {}

  @Roles(["PLATFORM_ADMIN"])
  @Get()
  public async getAll(@Query() query: ListAllEntitiesQueryDto) {
    return await this.ownersService.findAll(query);
  }

  @Roles(["PLATFORM_ADMIN"])
  @Get(":id")
  public async get(@Param() { id }: IdParamDto) {
    return await this.ownersService.findUniqueOwner({ userId: id });
  }

  @Roles(["PLATFORM_ADMIN"])
  @Post()
  @ApiDocCreateOwner()
  public async create(@Body() createOwnerDto: CreateOwnerDto) {
    return await this.ownersService.create(createOwnerDto);
  }
}
