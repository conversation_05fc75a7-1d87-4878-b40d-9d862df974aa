import { Body, Controller, Get, Param, Post, Query } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { StudentsService } from "./students.service.js";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import { CreateStudentDto } from "./dto/students.dto.js";
import { AcademicSessionIdParamDto } from "../../../shared/dto/param.dto.js";
import { ListStudentsQueryDto } from "./dto/students-query.dto.js";

@Controller("sms/academic-sessions")
@ApiTags("Students")
export class StudentsController {
  public constructor(private readonly studentsService: StudentsService) {}

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Post(":sessionId/students")
  public create(
    @Body() createStudentDto: CreateStudentDto,
    @Param() param: AcademicSessionIdParamDto,
  ) {
    return this.studentsService.create({
      ...createStudentDto,
      academicSessionId: param.sessionId,
    });
  }

  @Roles(["BRANCH_ADMIN", "INSTITUTE_OWNER"])
  @Get(":sessionId/students")
  public getAll(
    @Param() param: AcademicSessionIdParamDto,
    @Query() query: ListStudentsQueryDto,
  ) {
    return this.studentsService.findAllByAcademicSessionId(
      param.sessionId,
      query,
    );
  }
}
