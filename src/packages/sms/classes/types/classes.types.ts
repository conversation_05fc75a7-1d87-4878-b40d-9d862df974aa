import { Insertable, Selectable, Updateable } from "kysely";
import { ClassTable } from "../../../../database/types.js";
import { classResponseSchema, createClassSchema } from "../dto/classes.dto.js";
import { z } from "zod";

export type Class = Selectable<ClassTable>;
export type NewClass = Insertable<ClassTable>;
export type ClassUpdate = Updateable<ClassTable>;

export type CreateClassPayload = z.infer<typeof createClassSchema> & {
  academicSessionId: string;
};

export type UpdateClassPayload = Partial<CreateClassPayload>;

export type ClassResponse = z.infer<typeof classResponseSchema>;
