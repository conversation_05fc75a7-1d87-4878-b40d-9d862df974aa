import { Module } from "@nestjs/common";
import { AcademicSessionsService } from "./academic-sessions.service.js";
import { AcademicSessionsController } from "./academic-sessions.controller.js";
import { BranchesModule } from "../branches/branches.module.js";

@Module({
  imports: [BranchesModule],
  controllers: [AcademicSessionsController],
  providers: [AcademicSessionsService],
  exports: [AcademicSessionsService],
})
export class AcademicSessionsModule {}
