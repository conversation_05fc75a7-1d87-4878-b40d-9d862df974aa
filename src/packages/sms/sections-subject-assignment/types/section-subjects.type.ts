import { Insertable, Selectable, Updateable } from "kysely";
import { SectionSubjectTable } from "../../../../database/types.js";
import { sectionSubjectAssignmentResponseSchema } from "../dto/section-subjects.dto.js";
import { z } from "zod";

export type NewSectionSubject = Insertable<SectionSubjectTable>;
export type SectionSubject = Selectable<SectionSubjectTable>;
export type SectionSubjectUpdate = Updateable<SectionSubjectTable>;

export type AssignSubjectToSectionPayload = Insertable<SectionSubjectTable>;

export type SectionSubjectAssignmentResponse = z.infer<
  typeof sectionSubjectAssignmentResponseSchema
>;
