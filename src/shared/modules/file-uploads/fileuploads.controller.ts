import {
  Controller,
  ParseFilePipeBuilder,
  Post,
  UploadedFile,
  UseInterceptors,
} from "@nestjs/common";
import { FileInterceptor } from "@nestjs/platform-express";
import { ApiTags } from "@nestjs/swagger";
import { FileUploadService } from "./fileuploads.service.js";
import { FileSizeValidatorPipe } from "./pipes/fileSize-validator.pipe.js";
import { FileTypeValidatorPipe } from "./pipes/fileType-validator.pipe.js";

@ApiTags("File Uploads")
@Controller("sms/upload")
export class FileUploadController {
  public constructor(private readonly uploadService: FileUploadService) {}

  @Post("/image")
  @UseInterceptors(FileInterceptor("file"))
  public uploadImage(
    @UploadedFile(new FileSizeValidatorPipe(), new FileTypeValidatorPipe())
    file: Express.Multer.File,
  ) {
    return this.uploadService.handleFileUpload(file);
  }

  @Post("/pdf")
  @UseInterceptors(FileInterceptor("file"))
  public uploadPdf(
    @UploadedFile(
      new ParseFilePipeBuilder()
        .addFileTypeValidator({
          fileType: "application/pdf",
        })
        .addMaxSizeValidator({
          maxSize: 1024 * 1024 * 20, // 20MB,
        })
        .build(),
    )
    file: Express.Multer.File,
  ) {
    return this.uploadService.handleFileUpload(file);
  }
}
