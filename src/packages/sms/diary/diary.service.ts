import { Injectable, Logger } from "@nestjs/common";
import { InjectKysely } from "../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { Kysely, sql } from "kysely";
import { DB } from "../../../database/types.js";

import {
  NewDiary,
  DiaryResponse,
  ListDiariesQueryOptions,
} from "./types/diary.type.js";
import { handleDatabaseInsertException } from "../../../utils/pg-utils.js";
import { ClassSectionsService } from "../classes/sections/class-sections.service.js";
import { SubjectsService } from "../subjects/subjects.service.js";
import { EntityId } from "../../../shared/types/shared.types.js";

@Injectable()
export class DiaryService {
  private readonly logger = new Logger(DiaryService.name);

  constructor(
    @InjectKysely() private readonly db: Kysely<DB>,
    private readonly classSectionsService: ClassSectionsService,
    private readonly subjectsService: SubjectsService,
  ) {}

  public async create(newDiaryData: NewDiary): Promise<EntityId> {
    try {
      await this.classSectionsService.verifyClassSectionExists(
        newDiaryData.classSectionId,
      );

      await this.subjectsService.findSubjectOrThrow(newDiaryData.subjectId);

      return await this.db
        .insertInto("diary")
        .values(newDiaryData)
        .returning(["id"])
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      this.logger.error("Failed to create diary", error);
      handleDatabaseInsertException(error, {
        resource: "diary",
        logger: this.logger,
      });
    }
  }

  /**
   * Find all diary entries for a specific class section with optional filtering
   * Supports filtering by date, month, date range, and subject
   */
  public async findAllBySectionId(
    classSectionId: string,
    queryOptions: ListDiariesQueryOptions,
  ) {
    try {
      // Verify that the class section exists
      await this.classSectionsService.verifyClassSectionExists(classSectionId);

      // Build the base query with joins
      let query = this.selectDiariesQuery().where(
        "diary.classSectionId",
        "=",
        classSectionId,
      );

      // Apply date filters
      if (queryOptions.date) {
        query = query.where(sql`DATE(diary.date)`, "=", queryOptions.date);
      } else if (queryOptions.month) {
        // Extract year and month for filtering
        const [year, month] = queryOptions.month.split("-");
        if (year && month) {
          query = query
            .where(sql`EXTRACT(YEAR FROM diary.date)`, "=", parseInt(year))
            .where(sql`EXTRACT(MONTH FROM diary.date)`, "=", parseInt(month));
        }
      } else if (queryOptions.from && queryOptions.to) {
        query = query
          .where(sql`DATE(diary.date)`, ">=", queryOptions.from)
          .where(sql`DATE(diary.date)`, "<=", queryOptions.to);
      }

      // Apply subject filter
      if (queryOptions.subjectId) {
        query = query.where("diary.subjectId", "=", queryOptions.subjectId);
      }

      // Apply pagination and ordering
      const itemsQuery = query
        .limit(queryOptions.limit)
        .offset(queryOptions.offset)
        .orderBy("diary.date", "desc")
        .orderBy("diary.createdAt", "desc");

      // Count total items with the same filters
      let countQuery = this.db
        .selectFrom("diary")
        .where("diary.classSectionId", "=", classSectionId);

      // Apply the same filters to count query
      if (queryOptions.date) {
        countQuery = countQuery.where(
          sql`DATE(diary.date)`,
          "=",
          queryOptions.date,
        );
      } else if (queryOptions.month) {
        const [year, month] = queryOptions.month.split("-");
        if (year && month) {
          countQuery = countQuery
            .where(sql`EXTRACT(YEAR FROM diary.date)`, "=", parseInt(year))
            .where(sql`EXTRACT(MONTH FROM diary.date)`, "=", parseInt(month));
        }
      } else if (queryOptions.from && queryOptions.to) {
        countQuery = countQuery
          .where(sql`DATE(diary.date)`, ">=", queryOptions.from)
          .where(sql`DATE(diary.date)`, "<=", queryOptions.to);
      }

      if (queryOptions.subjectId) {
        countQuery = countQuery.where(
          "diary.subjectId",
          "=",
          queryOptions.subjectId,
        );
      }

      // Execute both queries in parallel
      const [items, { total }] = await Promise.all([
        itemsQuery.execute(),
        countQuery
          .select(({ fn }) => [fn.count("diary.id").as("total")])
          .executeTakeFirstOrThrow(),
      ]);

      return { items, total };
    } catch (error: unknown) {
      this.logger.error("Failed to find diaries by section id", error);
      throw error;
    }
  }

  /**
   * Build the base query for selecting diary entries with all related data
   * Includes joins with subject, class section, and class tables
   */
  private selectDiariesQuery() {
    return this.db
      .selectFrom("diary")
      .innerJoin("subject", "subject.id", "diary.subjectId")
      .innerJoin("classSection", "classSection.id", "diary.classSectionId")
      .innerJoin("class", "class.id", "classSection.classId")
      .select([
        "diary.id",
        "diary.content",
        "diary.date",
        "diary.createdAt",
        // Subject information
        sql<DiaryResponse["subject"]>`json_build_object(
          'id', subject.id,
          'name', subject.name,
          'type', subject.type,
          'marks', subject.marks
        )`.as("subject"),
        // Class section information
        sql<DiaryResponse["classSection"]>`json_build_object(
          'id', class_section.id,
          'name', class_section.name,
          'class', json_build_object(
            'id', class.id,
            'name', class.name
          )
        )`.as("classSection"),
      ]);
  }
}
