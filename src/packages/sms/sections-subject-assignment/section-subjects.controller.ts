import {
  Body,
  Controller,
  HttpStatus,
  Param,
  Post,
  Get,
  Query,
  Res,
  Patch,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { SectionSubjectsService } from "./section-subjects.service.js";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import {
  AssignSubjectToSectionDto,
  UpdateSectionSubjectDto,
} from "./dto/section-subjects.dto.js";
import type { Response } from "express";
import { AcademicSessionIdParamDto } from "../../../shared/dto/param.dto.js";
import { ListAllEntitiesQueryDto } from "../../../shared/dto/query.dto.js";
import { SectionSubjectParamsDto } from "./dto/section-subjects-param.dto.js";

@Controller("sms/academic-sessions")
@ApiTags("Section Subjects")
export class SectionSubjectsController {
  constructor(
    private readonly sectionSubjectsService: SectionSubjectsService,
  ) {}

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Post(":sessionId/class-section-subjects")
  public async assignSubject(
    @Body() assignSubjectDto: AssignSubjectToSectionDto,
    @Param() param: AcademicSessionIdParamDto,
    @Res() res: Response,
  ) {
    const { id } = await this.sectionSubjectsService.assignSubjectToSection({
      ...assignSubjectDto,
      academicSessionId: param.sessionId,
    });
    res.setHeader("Location", id);
    res.status(HttpStatus.CREATED).send();
  }

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Get(":sessionId/class-section-subjects")
  public async getAll(
    @Param() param: AcademicSessionIdParamDto,
    @Query() query: ListAllEntitiesQueryDto,
  ) {
    return this.sectionSubjectsService.findAllByAcademicSessionId(
      param.sessionId,
      query,
    );
  }

  @Roles(["INSTITUTE_OWNER", "BRANCH_ADMIN"])
  @Patch(":sessionId/class-section-subjects/:sectionSubjectId")
  public async updateSubjectAssignment(
    @Param() param: SectionSubjectParamsDto,
    @Body() updateSectionSubjectDto: UpdateSectionSubjectDto,
  ) {
    return this.sectionSubjectsService.updateSubjectAssignment(
      param.sectionSubjectId,
      updateSectionSubjectDto,
    );
  }
}
