import { Body, Controller, Get, Param, Post, Query } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { EnrollmentService } from "./enrollments.service.js";
import { Roles } from "../../../../core/roles/decorators/roles.decorator.js";
import { EnrollmentsParamsDto } from "./dto/enrollments-param.dto.js";
import { ListAllEntitiesQueryDto } from "../../../../shared/dto/query.dto.js";
import { CreateEnrollmentDto } from "./dto/enrollments.dto.js";

@ApiTags("Enrollments")
@Controller("sms/academic-sessions")
export class EnrollmentsController {
  public constructor(private readonly enrollmentsService: EnrollmentService) {}

  @Roles(["BRANCH_ADMIN", "INSTITUTE_OWNER"])
  @Get(":academicSessionId/enrollments")
  public async getAll(
    @Param() param: EnrollmentsParamsDto,
    @Query() query: ListAllEntitiesQueryDto,
  ) {
    return await this.enrollmentsService.findAllByAcademicSessionId(
      param.academicSessionId,
      query,
    );
  }

  @Roles(["BRANCH_ADMIN", "INSTITUTE_OWNER"])
  @Post(":academicSessionId/enrollments")
  public async create(
    @Body() createEnrollmentDto: CreateEnrollmentDto,
    @Param() param: EnrollmentsParamsDto,
  ) {
    return await this.enrollmentsService.create({
      ...createEnrollmentDto,
      academicSessionId: param.academicSessionId,
    });
  }
}
