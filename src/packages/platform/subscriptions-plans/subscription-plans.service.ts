import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { Kysely } from "kysely";
import { InjectKysely } from "../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { ListAllEntitiesQueryOptions } from "../../../shared/types/shared.types.js";
import { DB } from "../../../database/types.js";
import {
  NewSubscriptionPlan,
  SubscriptionPlan,
  SubscriptionPlanUpdate,
} from "./types/subscription-plans.types.js";
import { handleDatabaseInsertException } from "../../../utils/pg-utils.js";

@Injectable()
export class SubscriptionPlansService {
  private readonly logger = new Logger(SubscriptionPlansService.name);

  public constructor(@InjectKysely() private readonly db: Kysely<DB>) {}

  public async create(data: NewSubscriptionPlan) {
    try {
      return await this.db
        .insertInto("subscriptionPlan")
        .values(data)
        .returningAll()
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      handleDatabaseInsertException(error, {
        resource: "subscriptionPlan",
        logger: this.logger,
      });
    }
  }

  public async update(
    id: SubscriptionPlan["id"],
    data: SubscriptionPlanUpdate,
  ) {
    const existingSubscriptionPlan = await this.findById(id);

    if (!existingSubscriptionPlan) {
      throw new NotFoundException(
        `Software Package with id: '${id}' does not exist`,
      );
    }

    try {
      return await this.db
        .updateTable("subscriptionPlan")
        .set(data)
        .where("id", "=", id)
        .returningAll()
        .executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to update software package", error);
      throw error;
    }
  }

  public async delete(id: SubscriptionPlan["id"]) {
    const existingSubscriptionPlan = await this.findById(id);

    if (!existingSubscriptionPlan) {
      throw new NotFoundException(
        `Software Package with id: '${id}' does not exist`,
      );
    }

    await this.db.deleteFrom("subscriptionPlan").where("id", "=", id).execute();
  }

  public async getAll({ limit, offset }: ListAllEntitiesQueryOptions) {
    return await this.db
      .selectFrom("subscriptionPlan")
      .selectAll()
      .limit(limit)
      .offset(offset)
      .orderBy("createdAt", "asc")
      .execute();
  }

  public async findById(id: SubscriptionPlan["id"]) {
    return await this.db
      .selectFrom("subscriptionPlan")
      .selectAll()
      .where("id", "=", id)
      .executeTakeFirst();
  }

  private async findUniquePackage({
    price,
    title,
  }: Pick<SubscriptionPlan, "price" | "title">) {
    return await this.db
      .selectFrom("subscriptionPlan")
      .selectAll()
      .where("title", "=", title)
      .where("price", "=", price)
      .executeTakeFirst();
  }
}
