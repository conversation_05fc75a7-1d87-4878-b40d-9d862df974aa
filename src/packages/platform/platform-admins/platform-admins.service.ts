import { Injectable, Logger } from "@nestjs/common";
import { InjectKysely } from "../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { Kysely } from "kysely";
import { DB } from "../../../database/types.js";

@Injectable()
export class PlatformAdminsService {
  private logger = new Logger(PlatformAdminsService.name);

  constructor(@InjectKysely() private readonly db: Kysely<DB>) {}

  async findByEmailIncludePassword(email: string) {
    try {
      const platformAdmin = await this.db
        .selectFrom("platformAdmin")
        .innerJoin("users", "users.id", "platformAdmin.userId")
        .selectAll("platformAdmin")
        .select([
          "users.email as email",
          "users.name as name",
          "users.password as password",
          "users.roleId as roleId",
        ])
        .where("email", "=", email)
        .executeTakeFirst();
      return platformAdmin;
    } catch (error: unknown) {
      this.logger.error("Failed to get platform admin by email", error);
      throw error;
    }
  }
}
