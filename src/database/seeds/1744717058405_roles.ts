import type { Kys<PERSON> } from "kysely";
import { <PERSON> } from "../types.js";
import { ROLES } from "../../core/roles/constants/roles.constants.js";

export async function seed(db: Kysely<DB>): Promise<void> {
  try {
    // seed roles
    await db
      .insertInto("role")
      .values(Object.values(ROLES))
      .onConflict(oc => oc.doNothing())
      .execute();
  } catch (error: unknown) {
    console.error("Failed to seed roles data", error);
    throw error;
  }
}
