services:
  server:
    build:
      context: .
      target: ${DOCKER_STAGE:-dev}
    env_file:
      - .env
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      DATABASE_HOST: db
      DATABASE_USER: postgres
      DATABASE_PASSWORD_FILE: /run/secrets/db-password
      DATABASE_NAME: ${DATABASE_NAME:-example}
    ports:
      - 3000:3000
      - 9229:9229
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./src:/usr/app/src
    secrets:
      - db-password
  db:
    image: postgres
    restart: always
    user: postgres
    secrets:
      - db-password
    volumes:
      - db-data:/var/lib/postgresql/data
    env_file:
      - .env
    environment:
      POSTGRES_DB: ${DATABASE_NAME:-example}
      POSTGRES_PASSWORD_FILE: /run/secrets/db-password
    expose:
      - 5432
    healthcheck:
      test: ["CMD", "pg_isready"]
      interval: 10s
      timeout: 5s
      retries: 5
volumes:
  db-data:
secrets:
  db-password:
    file: secrets/password.txt
