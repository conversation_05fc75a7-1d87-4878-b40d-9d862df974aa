import { type Kysely, sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable("class")
    .addColumn("id", "uuid", col =>
      col.primaryKey().defaultTo(sql`gen_random_uuid()`),
    )
    .addColumn("name", "text", col => col.notNull())
    .addColumn("maximumStudents", "integer", col => col.notNull())
    .addColumn("isActive", "boolean", col => col.notNull().defaultTo(true))
    .addColumn("academicSessionId", "uuid", col =>
      col
        .notNull()
        .references("academicSession.id")
        .onDelete("restrict")
        .onUpdate("cascade"),
    )
    .addColumn("feePerMonth", "numeric(10, 2)", col => col.notNull())
    .addColumn("createdAt", "timestamptz", col =>
      col.notNull().defaultTo(sql`NOW()`),
    )
    .addUniqueConstraint("class_name_academicSessionId_unique", [
      "name",
      "academicSessionId",
    ])
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("class").execute();
}
