import { Module } from "@nestjs/common";
import { UsersService } from "./users.service.js";
import { RolesModule } from "../roles/roles.module.js";
import { UsersController } from "./users.controller.js";
import { InstituteOwnersModule } from "../../packages/sms/institute-owners/owners.module.js";
import { StaffModule } from "../../packages/sms/staff/staff.module.js";

@Module({
  imports: [RolesModule, InstituteOwnersModule, StaffModule],
  controllers: [UsersController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
