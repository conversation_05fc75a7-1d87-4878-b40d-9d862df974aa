import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { InstitutesService } from "./institutes.service.js";
import { createInstituteDto, UpdateInstituteDto } from "./dto/institute.dto.js";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import { ListAllEntitiesQueryDto } from "../../../shared/dto/query.dto.js";
import type { Request } from "express";
import {
  ApiDocCreateInstitute,
  ApiDocGetOwnerInstitute as ApiDocGetCurrentUserInstitute,
} from "./docs/institute.docs.js";
import { InstituteParamDto } from "./dto/institute-param.dto.js";

@Controller("sms/institutes")
@ApiTags("Institutes")
export class InstitutesController {
  public constructor(private readonly instituteService: InstitutesService) {}

  @Roles(["PLATFORM_ADMIN"])
  @Get()
  public async getAll(@Query() query: ListAllEntitiesQueryDto) {
    return await this.instituteService.findAll(query);
  }

  @Roles(["INSTITUTE_OWNER"])
  @Get("/me")
  @ApiDocGetCurrentUserInstitute()
  public async getOwnerInstitute(@Req() req: Request) {
    const userId = req.user.sub;
    return this.instituteService.findOwnerInstitute(userId);
  }

  @Roles(["INSTITUTE_OWNER"])
  @Patch("/me")
  public async updateOwnerInstitute(
    @Req() req: Request,
    @Body() updateInstituteDto: UpdateInstituteDto,
  ) {
    const userId = req.user.sub;
    return this.instituteService.updateOwnerInstitute(
      userId,
      updateInstituteDto,
    );
  }

  @Roles(["INSTITUTE_OWNER"])
  @Patch("/:instituteId")
  public async update(
    @Body() updateInstituteDto: UpdateInstituteDto,
    @Param() param: InstituteParamDto,
  ) {
    return this.instituteService.update(param.instituteId, updateInstituteDto);
  }

  @Roles(["INSTITUTE_OWNER"])
  @Post()
  @ApiDocCreateInstitute()
  public async create(
    @Body() createInstituteDto: createInstituteDto,
    @Req() req: Request,
  ) {
    const userId = req.user.sub;
    return this.instituteService.create(createInstituteDto, userId);
  }
}
