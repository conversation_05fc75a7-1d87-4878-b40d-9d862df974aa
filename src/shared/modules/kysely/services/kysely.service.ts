/* eslint-disable @darraghor/nestjs-typed/injectable-should-be-provided */
import {
  Injectable,
  Logger,
  OnApplicationShutdown,
  OnModuleInit,
} from "@nestjs/common";
import { Kysely, sql } from "kysely";

@Injectable()
export class KyselyService implements OnModuleInit, OnApplicationShutdown {
  private readonly activeClients = new Set<Kysely<any>>();
  private readonly logger = new Logger(KyselyService.name);

  public addClient<T>(client: Kysely<T>) {
    this.activeClients.add(client);
  }

  async onModuleInit() {
    const client = Array.from(this.activeClients)[0];

    try {
      if (!client) {
        throw new Error("No Kysely client found");
      }
      await sql`SELECT 1`.execute(client);
      this.logger.log("✅ PostgreSQL is ready");
      return;
    } catch (error: unknown) {
      this.logger.error("Failed to connect to database", error);
    }
  }

  public async onApplicationShutdown() {
    for (const client of this.activeClients) {
      try {
        await client.destroy();
      } catch (error: unknown) {
        this.logger.error("Failed to destroy Kysely client ", error);
      }
    }
  }
}
