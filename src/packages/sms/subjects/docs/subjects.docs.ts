import {
  ApiO<PERSON>ation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
} from "@nestjs/swagger";
import { applyDecorators } from "@nestjs/common";
import {
  CreateSubjectDto,
  UpdateSubjectDto,
  SubjectResponseDto,
} from "../dto/subjects.dto.js";
import { commonApiResponseOptions } from "../../../../docs/shared/api-responses.js";

/**
 * Swagger documentation for the get all subjects endpoint
 * Retrieves all subjects for a specific academic session with pagination
 */
export function ApiDocGetAllSubjects() {
  return applyDecorators(
    ApiOperation({
      summary: "Get All Subjects",
      description: `
        Retrieves all subjects for a specific academic session with pagination support.

        **Features:**
        - **Pagination**: Use \`limit\` and \`offset\` parameters to control the number of results
        - **Ordering**: Results are ordered by creation date (most recent first)
        - **Session-specific**: Only returns subjects for the specified academic session

        **Authentication Required:** Bearer token must be provided in the Authorization header.

        **Permissions:** Only Institute Owners can view subjects.

        **Use Cases:**
        - Viewing all subjects configured for an academic session
        - Managing curriculum and subject offerings
        - Setting up class schedules and teacher assignments

        **Response Structure:**
        - Returns paginated list of subjects with complete details
        - Includes total count for pagination
        - Each subject includes: name, type, marks, active status, and timestamps
      `,
    }),
    ApiParam({
      name: "sessionId",
      required: true,
      description: "Unique identifier of the academic session",
      type: String,
      example: "123e4567-e89b-12d3-a456-426614174000",
    }),
    ApiQuery({
      name: "limit",
      required: false,
      description: "Maximum number of subjects to retrieve",
      type: Number,
      example: 10,
    }),
    ApiQuery({
      name: "offset",
      required: false,
      description: "Number of subjects to skip for pagination",
      type: Number,
      example: 0,
    }),
    ApiResponse({
      status: 200,
      description: "Successfully retrieved subjects",
      content: {
        "application/json": {
          schema: {
            type: "object",
            properties: {
              statusCode: { type: "number", example: 200 },
              message: {
                type: "string",
                example: "Subjects retrieved successfully",
              },
              data: {
                type: "object",
                properties: {
                  items: {
                    type: "array",
                    items: { $ref: "#/components/schemas/SubjectResponseDto" },
                  },
                  total: { type: "number", example: 15 },
                },
              },
            },
          },
          examples: {
            "subjects-list": {
              summary: "List of Subjects",
              description: "Example response with multiple subjects",
              value: {
                statusCode: 200,
                message: "Subjects retrieved successfully",
                data: {
                  items: [
                    {
                      id: "subject-123",
                      name: "Mathematics",
                      type: "THEORY",
                      marks: 100,
                      isActive: true,
                      academicSessionId: "session-456",
                      createdAt: "2024-01-15T10:30:00.000Z",
                    },
                    {
                      id: "subject-456",
                      name: "Physics Lab",
                      type: "PRACTICAL",
                      marks: 50,
                      isActive: true,
                      academicSessionId: "session-456",
                      createdAt: "2024-01-14T14:20:00.000Z",
                    },
                  ],
                  total: 15,
                },
              },
            },
          },
        },
      },
    }),
    ApiResponse(commonApiResponseOptions.BAD_REQUEST),
    ApiResponse(commonApiResponseOptions.FORBIDDEN),
    ApiResponse(commonApiResponseOptions.NOT_FOUND),
    ApiResponse(commonApiResponseOptions.INTERNAL_SERVER_ERROR),
  );
}

/**
 * Swagger documentation for the create subject endpoint
 * Creates a new subject for a specific academic session
 */
export function ApiDocCreateSubject() {
  return applyDecorators(
    ApiOperation({
      summary: "Create Subject",
      description: `
        Creates a new subject for a specific academic session.

        **Required Information:**
        - **Session ID**: Must be a valid academic session ID (provided in URL path)
        - **Name**: Subject name (max 100 characters, must be unique within session)
        - **Type**: Either "THEORY" or "PRACTICAL"
        - **Marks**: Maximum marks for the subject (1-1000)

        **Authentication Required:** Bearer token must be provided in the Authorization header.

        **Permissions:** Only Institute Owners can create subjects.

        **Validation Rules:**
        - Subject name must be unique within the academic session and type combination
        - Marks must be between 1 and 1000
        - Type must be either THEORY or PRACTICAL
        - Academic session must exist and be accessible

        **Use Cases:**
        - Setting up curriculum for a new academic session
        - Adding new subjects to existing academic sessions
        - Configuring subject-specific settings like marks allocation

        **Business Logic:**
        - Subjects are automatically set as active upon creation
        - Each subject is tied to a specific academic session
        - Subject name + session + type combination must be unique
      `,
    }),
    ApiParam({
      name: "sessionId",
      required: true,
      description: "Unique identifier of the academic session",
      type: String,
      example: "123e4567-e89b-12d3-a456-426614174000",
    }),
    ApiBody({
      type: CreateSubjectDto,
      description: "Subject data including name, type, and marks",
      examples: {
        "mathematics-theory": {
          summary: "Mathematics Theory Subject",
          description: "Example for creating a theoretical mathematics subject",
          value: {
            name: "Mathematics",
            type: "THEORY",
            marks: 100,
          },
        },
        "physics-practical": {
          summary: "Physics Practical Subject",
          description: "Example for creating a practical physics subject",
          value: {
            name: "Physics Laboratory",
            type: "PRACTICAL",
            marks: 50,
          },
        },
        "computer-science": {
          summary: "Computer Science Subject",
          description: "Example for creating a computer science subject",
          value: {
            name: "Computer Science",
            type: "THEORY",
            marks: 75,
          },
        },
      },
    }),
    ApiResponse({
      status: 201,
      description: "Subject created successfully",
      type: SubjectResponseDto,
      content: {
        "application/json": {
          examples: {
            "created-subject": {
              summary: "Created Subject",
              description: "Example response after successful subject creation",
              value: {
                statusCode: 201,
                message: "Subject created successfully",
                data: {
                  id: "abc123def-456g-789h-012i-345678901234",
                  name: "Mathematics",
                  type: "THEORY",
                  marks: 100,
                  isActive: true,
                  academicSessionId: "123e4567-e89b-12d3-a456-426614174000",
                  createdAt: "2024-01-15T10:30:00.000Z",
                },
              },
            },
          },
        },
      },
    }),
    ApiResponse(commonApiResponseOptions.BAD_REQUEST),
    ApiResponse(commonApiResponseOptions.FORBIDDEN),
    ApiResponse(commonApiResponseOptions.NOT_FOUND),
    ApiResponse({
      ...commonApiResponseOptions.CONFLICT,
      description:
        "Conflict - Subject with same name and type already exists in this session",
    }),
    ApiResponse(commonApiResponseOptions.INTERNAL_SERVER_ERROR),
  );
}

/**
 * Swagger documentation for the update subject endpoint
 * Updates an existing subject with new information
 */
export function ApiDocUpdateSubject() {
  return applyDecorators(
    ApiOperation({
      summary: "Update Subject",
      description: `
        Updates an existing subject with new information. All fields are optional.

        **Required Information:**
        - **Session ID**: Must be a valid academic session ID (provided in URL path)
        - **Subject ID**: Must be a valid subject ID (provided in URL path)

        **Optional Fields:**
        - **Name**: New subject name (max 100 characters)
        - **Type**: New subject type ("THEORY" or "PRACTICAL")
        - **Marks**: New maximum marks (1-1000)

        **Authentication Required:** Bearer token must be provided in the Authorization header.

        **Permissions:** Only Institute Owners can update subjects.

        **Validation Rules:**
        - Subject must exist and be accessible
        - If changing name/type, the new combination must be unique within the session
        - Marks must be between 1 and 1000 if provided
        - Academic session must exist if being changed

        **Use Cases:**
        - Correcting subject information
        - Adjusting marks allocation for subjects
        - Changing subject type from theory to practical or vice versa
        - Updating subject names for clarity

        **Business Logic:**
        - Only provided fields will be updated (partial update)
        - Subject ID and creation timestamp cannot be changed
        - Academic session can be changed if needed
      `,
    }),
    ApiParam({
      name: "sessionId",
      required: true,
      description: "Unique identifier of the academic session",
      type: String,
      example: "123e4567-e89b-12d3-a456-426614174000",
    }),
    ApiParam({
      name: "subjectId",
      required: true,
      description: "Unique identifier of the subject to update",
      type: String,
      example: "456e7890-e89b-12d3-a456-426614174001",
    }),
    ApiBody({
      type: UpdateSubjectDto,
      description: "Subject update data (all fields optional)",
      examples: {
        "update-marks": {
          summary: "Update Subject Marks",
          description: "Example for updating only the marks of a subject",
          value: {
            marks: 80,
          },
        },
        "update-name-and-type": {
          summary: "Update Name and Type",
          description: "Example for updating subject name and type",
          value: {
            name: "Advanced Mathematics",
            type: "THEORY",
          },
        },
        "complete-update": {
          summary: "Complete Update",
          description: "Example for updating all subject fields",
          value: {
            name: "Physics Laboratory Advanced",
            type: "PRACTICAL",
            marks: 60,
          },
        },
      },
    }),
    ApiResponse({
      status: 200,
      description: "Subject updated successfully",
      type: SubjectResponseDto,
      content: {
        "application/json": {
          examples: {
            "updated-subject": {
              summary: "Updated Subject",
              description: "Example response after successful subject update",
              value: {
                statusCode: 200,
                message: "Subject updated successfully",
                data: {
                  id: "456e7890-e89b-12d3-a456-426614174001",
                  name: "Advanced Mathematics",
                  type: "THEORY",
                  marks: 80,
                  isActive: true,
                  academicSessionId: "123e4567-e89b-12d3-a456-426614174000",
                  createdAt: "2024-01-15T10:30:00.000Z",
                },
              },
            },
          },
        },
      },
    }),
    ApiResponse(commonApiResponseOptions.BAD_REQUEST),
    ApiResponse(commonApiResponseOptions.FORBIDDEN),
    ApiResponse(commonApiResponseOptions.NOT_FOUND),
    ApiResponse({
      ...commonApiResponseOptions.CONFLICT,
      description:
        "Conflict - Subject with same name and type already exists in this session",
    }),
    ApiResponse(commonApiResponseOptions.INTERNAL_SERVER_ERROR),
  );
}

/**
 * Swagger documentation for the get subject by ID endpoint
 * Retrieves a specific subject by its ID
 */
export function ApiDocGetSubjectById() {
  return applyDecorators(
    ApiOperation({
      summary: "Get Subject by ID",
      description: `
        Retrieves detailed information about a specific subject by its ID.

        **Required Information:**
        - **Session ID**: Must be a valid academic session ID (provided in URL path)
        - **Subject ID**: Must be a valid subject ID (provided in URL path)

        **Authentication Required:** Bearer token must be provided in the Authorization header.

        **Permissions:** Only Institute Owners can view subject details.

        **Use Cases:**
        - Viewing detailed information about a specific subject
        - Verifying subject configuration before making changes
        - Displaying subject details in administrative interfaces
        - Checking subject information for reporting purposes

        **Response Details:**
        - Returns complete subject information including all fields
        - Includes metadata like creation timestamp and active status
        - Shows associated academic session ID for reference

        **Error Handling:**
        - Returns 404 if subject doesn't exist
        - Returns 403 if user doesn't have permission to view the subject
        - Returns 400 if the provided IDs are invalid
      `,
    }),
    ApiParam({
      name: "sessionId",
      required: true,
      description: "Unique identifier of the academic session",
      type: String,
      example: "123e4567-e89b-12d3-a456-426614174000",
    }),
    ApiParam({
      name: "subjectId",
      required: true,
      description: "Unique identifier of the subject to retrieve",
      type: String,
      example: "456e7890-e89b-12d3-a456-426614174001",
    }),
    ApiResponse({
      status: 200,
      description: "Successfully retrieved subject details",
      type: SubjectResponseDto,
      content: {
        "application/json": {
          examples: {
            "theory-subject": {
              summary: "Theory Subject",
              description: "Example response for a theoretical subject",
              value: {
                statusCode: 200,
                message: "Subject retrieved successfully",
                data: {
                  id: "456e7890-e89b-12d3-a456-426614174001",
                  name: "Mathematics",
                  type: "THEORY",
                  marks: 100,
                  isActive: true,
                  academicSessionId: "123e4567-e89b-12d3-a456-426614174000",
                  createdAt: "2024-01-15T10:30:00.000Z",
                },
              },
            },
            "practical-subject": {
              summary: "Practical Subject",
              description: "Example response for a practical subject",
              value: {
                statusCode: 200,
                message: "Subject retrieved successfully",
                data: {
                  id: "789e0123-e89b-12d3-a456-426614174002",
                  name: "Chemistry Laboratory",
                  type: "PRACTICAL",
                  marks: 50,
                  isActive: true,
                  academicSessionId: "123e4567-e89b-12d3-a456-426614174000",
                  createdAt: "2024-01-14T14:20:00.000Z",
                },
              },
            },
          },
        },
      },
    }),
    ApiResponse(commonApiResponseOptions.BAD_REQUEST),
    ApiResponse(commonApiResponseOptions.FORBIDDEN),
    ApiResponse(commonApiResponseOptions.NOT_FOUND),
    ApiResponse(commonApiResponseOptions.INTERNAL_SERVER_ERROR),
  );
}
