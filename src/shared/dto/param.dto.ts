import { createZodDto } from "nestjs-zod/dto";
import z from "zod";
import { getUuidSchema } from "../schema/zod-common.schema.js";

const paramSchema = z.object({
  id: getUuidSchema("Resource"),
});

export class IdParamDto extends createZodDto(paramSchema) {}

// ----------- Session-Id-Param-Dto ------------>
export const academicSessionIdParamSchema = z.object({
  sessionId: getUuidSchema("Academic Session ID"),
});
export class AcademicSessionIdParamDto extends createZodDto(
  academicSessionIdParamSchema,
) {}
