import {
  BadRequestException,
  Injectable,
  PipeTransform,
  UnprocessableEntityException,
} from "@nestjs/common";

@Injectable()
export class FileTypeValidatorPipe implements PipeTransform {
  private readonly allowedTypes = ["image/jpeg", "image/png", "image/jpg"];

  transform(file?: Express.Multer.File) {
    if (!file) throw new BadRequestException("Image file is required");
    const isValid = this.allowedTypes.includes(file.mimetype);
    if (!isValid) {
      throw new UnprocessableEntityException(
        "Image type should be jpeg , jpg or png",
      );
    }
    return file;
  }
}
