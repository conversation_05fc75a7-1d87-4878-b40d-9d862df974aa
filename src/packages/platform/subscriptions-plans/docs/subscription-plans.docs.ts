import { applyDecorators } from "@nestjs/common";
import {
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
  ApiQuery,
} from "@nestjs/swagger";
import { PackageDto } from "../dto/subscription-plans.dto.js";
import {
  commonApiResponseOptions,
  useCommonCreateResourceApiResponses,
} from "../../../../docs/shared/api-responses.js";

/**
 * Swagger decorator for the create package endpoint
 * Provides comprehensive API documentation for the package creation process
 */
export function ApiDocCreateSubscriptionPlan() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Create a new package",
      description: "Creates a new service package with the provided details",
    }),
    ApiResponse({
      status: 201,
      type: PackageDto,
      description: "Returns the created package with complete details",
    }),
  );
}

/**
 * Swagger decorator for the get all packages endpoint
 * Provides detailed API documentation for retrieving packages with filtering and pagination
 */
export function ApiDocGetAllSubscriptionPlans() {
  return applyDecorators(
    ApiOperation({
      summary: "Retrieve all packages",
      description:
        "Returns a list of all available service packages with optional filtering and pagination",
    }),
    ApiQuery({
      name: "offset",
      required: false,
      description: "Number of items to skip",
      type: Number,
      example: 1,
    }),
    ApiQuery({
      name: "limit",
      required: false,
      description: "Maximum number of items to retrieve",
      type: Number,
      example: 10,
    }),
    ApiResponse({
      status: 200,
      description: "Successfully retrieved packages",
      schema: {
        type: "object",
        properties: {
          data: {
            type: "array",
            items: { $ref: "#/components/schemas/PackageDto" },
          },
          meta: {
            type: "object",
            properties: {
              total: { type: "number", example: 42 },
              page: { type: "number", example: 1 },
              limit: { type: "number", example: 10 },
              pages: { type: "number", example: 5 },
            },
          },
        },
      },
    }),
  );
}

/**
 * Swagger decorator for getting a specific package by ID
 * Provides detailed API documentation for retrieving a single package
 */
export function ApiDocGetSubscriptionPlanById() {
  return applyDecorators(
    ApiOperation({
      summary: "Retrieve a package by ID",
      description:
        "Returns detailed information about a specific service package",
    }),
    ApiParam({
      name: "id",
      required: true,
      description: "Unique identifier of the package",
      type: String,
      example: "507f1f77bcf86cd799439011",
    }),
    ApiResponse({
      status: 200,
      type: PackageDto,
      description: "Successfully retrieved package details",
    }),
    ApiResponse(commonApiResponseOptions.NOT_FOUND),
    ApiResponse(commonApiResponseOptions.BAD_REQUEST),
  );
}

/**
 * Swagger decorator for updating a package
 * Provides detailed API documentation for the package update process
 */
export function ApiDocUpdateSubscriptionPlan() {
  return applyDecorators(
    ApiOperation({
      summary: "Update an existing package",
      description: "Updates a service package with the provided details",
    }),
    ApiParam({
      name: "id",
      required: true,
      description: "Unique identifier of the package to update",
      type: String,
      example: "507f1f77bcf86cd799439011",
    }),
    ApiBody({
      type: PackageDto,
      description: "Package update payload containing the fields to update",
    }),
    ApiResponse({
      status: 200,
      type: PackageDto,
      description: "Successfully updated package with complete details",
    }),
    ApiResponse(commonApiResponseOptions.BAD_REQUEST),
    ApiResponse(commonApiResponseOptions.NOT_FOUND),
    ApiResponse(commonApiResponseOptions.CONFLICT),
    ApiResponse(commonApiResponseOptions.INTERNAL_SERVER_ERROR),
  );
}

/**
 * Swagger decorator for deleting a package
 * Provides detailed API documentation for the package deletion process
 */
export function ApiDocDeleteSubscription() {
  return applyDecorators(
    ApiOperation({
      summary: "Delete a package",
      description: "Permanently removes a service package from the system",
    }),
    ApiParam({
      name: "id",
      required: true,
      description: "Unique identifier of the package to delete",
      type: String,
      example: "507f1f77bcf86cd799439011",
    }),
    ApiResponse({
      status: 204,
      description: "Package successfully deleted",
    }),
    ApiResponse(commonApiResponseOptions.NOT_FOUND),
    ApiResponse(commonApiResponseOptions.INTERNAL_SERVER_ERROR),
  );
}
