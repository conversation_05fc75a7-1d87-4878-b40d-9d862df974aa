import { type Kysely, sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createType("subjectType")
    .asEnum(["THEORY", "PRACTICAL"])
    .execute();

  await db.schema
    .createTable("subject")
    .addColumn("id", "uuid", col =>
      col.primaryKey().defaultTo(sql`gen_random_uuid()`),
    )
    .addColumn("name", "text", col => col.notNull())
    .addColumn("marks", "integer", col => col.notNull())
    .addColumn("isActive", "boolean", col => col.notNull().defaultTo(true))
    .addColumn("academicSessionId", "uuid", col =>
      col.notNull().references("academicSession.id"),
    )
    .addColumn("type", sql`subject_type`, col => col.notNull())
    .addColumn("createdAt", "timestamptz", col =>
      col.notNull().defaultTo(sql`NOW()`),
    )
    .execute();

  await db.schema
    .createIndex("idx_subject_name_sessionId_type")
    .columns(["name", "academicSessionId", "type"])
    .on("subject")
    .unique()
    .execute();

  await db.schema
    .createIndex("idx_subject_sessionId")
    .columns(["academicSessionId"])
    .on("subject")
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("subject").execute();
  await db.schema.dropType("subjectType").execute();
}
