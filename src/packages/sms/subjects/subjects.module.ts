import { Modu<PERSON> } from "@nestjs/common";
import { SubjectsController } from "./subjects.controller.js";
import { SubjectsService } from "./subjects.service.js";
import { AcademicSessionsModule } from "../academic-sessions/academic-sessions.module.js";

@Module({
  imports: [AcademicSessionsModule],
  controllers: [SubjectsController],
  providers: [SubjectsService],
  exports: [SubjectsService],
})
export class SubjectsModule {}
