/* eslint-disable @darraghor/nestjs-typed/injectable-should-be-provided */
import { Injectable, Logger, OnModuleInit } from "@nestjs/common";
import { sql } from "drizzle-orm";
import { PostgresJsDatabase } from "drizzle-orm/postgres-js";
import postgres from "postgres";

export interface PostgresClient<T extends Record<string, unknown>>
  extends PostgresJsDatabase<T> {
  $client: postgres.Sql<Record<string, unknown>>;
}

@Injectable()
export class DrizzleService implements OnModuleInit {
  private readonly activeClients = new Set<PostgresJsDatabase<any>>();
  private readonly logger = new Logger(DrizzleService.name);

  public addClient<T extends Record<string, unknown>>(
    client: PostgresClient<T>,
  ) {
    this.activeClients.add(client);
  }

  async onModuleInit() {
    const client = Array.from(this.activeClients)[0];

    try {
      if (!client) {
        throw new Error("No Kysely client found");
      }
      await client.execute(sql`SELECT 1`);
      this.logger.log("✅ PostgreSQL is ready");
      return;
    } catch (error: unknown) {
      this.logger.error("Failed to connect to database", error);
    }
  }
}
