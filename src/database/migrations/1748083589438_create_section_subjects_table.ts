import { type Kysely, sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable("sectionSubject")
    .addColumn("id", "uuid", col =>
      col.primaryKey().defaultTo(sql`gen_random_uuid()`),
    )
    .addColumn("classSectionId", "uuid", col =>
      col
        .notNull()
        .references("classSection.id")
        .onDelete("restrict")
        .onUpdate("cascade"),
    )
    .addColumn("subjectId", "uuid", col =>
      col
        .notNull()
        .references("subject.id")
        .onDelete("restrict")
        .onUpdate("cascade"),
    )
    .addColumn("academicSessionId", "uuid", col =>
      col
        .notNull()
        .references("academicSession.id")
        .onDelete("restrict")
        .onUpdate("cascade"),
    )
    .addColumn("subjectTeacherId", "uuid", col =>
      col
        .notNull()
        .references("staff.id")
        .onDelete("restrict")
        .onUpdate("cascade"),
    )
    .addColumn("createdAt", "timestamptz", col =>
      col.notNull().defaultTo(sql`NOW()`),
    )
    .addUniqueConstraint("unique_subject_per_class_section", [
      "subjectId",
      "classSectionId",
      "academicSessionId",
    ])
    .execute();

  await db.schema
    .createIndex("idx_academicSessionId")
    .on("sectionSubject")
    .columns(["academicSessionId"])
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("sectionSubject").execute();
}
