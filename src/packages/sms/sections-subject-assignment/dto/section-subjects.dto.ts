import { z } from "zod";
import { createZodDto } from "nestjs-zod";
import { getUuidSchema } from "../../../../shared/schema/zod-common.schema.js";

const assignSubjectsToSectionSchema = z.object({
  classSectionId: getUuidSchema("Class Section ID"),
  subjectId: getUuidSchema("Subject ID"),
  subjectTeacherId: getUuidSchema("Subject Teacher ID"),
});

export class AssignSubjectToSectionDto extends createZodDto(
  assignSubjectsToSectionSchema,
) {}

// ------------------- Update-Section-Subject-Dto ------------------->
export const updateSectionSubjectSchema =
  assignSubjectsToSectionSchema.partial();
export class UpdateSectionSubjectDto extends createZodDto(
  updateSectionSubjectSchema,
) {}

// -------------------- Section-Subject-Response-Dto -------------------->
const resourceSchema = z.object({
  id: getUuidSchema("id"),
  name: z.string(),
});
export const sectionSubjectAssignmentResponseSchema = z.object({
  id: z.string(),
  academicSessionId: getUuidSchema("Academic Session ID"),
  class: resourceSchema,
  section: resourceSchema,
  subject: resourceSchema.extend({
    totalStudents: z.number(),
  }),
  subjectTeacher: resourceSchema,
  createdAt: z.date(),
});
