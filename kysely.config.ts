import { defineConfig } from "kysely-ctl";
import { Pool } from "pg";
import { CamelCasePlugin } from "kysely";
import { join } from "path";
import { readFileSync } from "fs";

export default defineConfig({
  dialect: "pg",
  dialectConfig: {
    pool: new Pool({
      host: process.env.DATABASE_HOST,
      port: Number(process.env.DATABASE_PORT),
      user: process.env.DATABASE_USER,
      password:
        process.env.DATABASE_PASSWORD ??
        readFileSync(
          process.env.DATABASE_PASSWORD_FILE ?? "/run/secrets/db-password",
          "utf-8",
        ),
      database: process.env.DATABASE_NAME,
    }),
  },
  plugins: [new CamelCasePlugin()],
  migrations: {
    migrationFolder: join("src/database", "migrations"),
  },
  seeds: {
    seedFolder: join("src/database", "seeds"),
  },
});
