import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { SectionSubjectsController } from "./section-subjects.controller.js";
import { SectionSubjectsService } from "./section-subjects.service.js";
import { ClassSectionsModule } from "../classes/sections/class-sections.module.js";
import { AcademicSessionsModule } from "../academic-sessions/academic-sessions.module.js";
import { StaffModule } from "../staff/staff.module.js";
import { SubjectsModule } from "../subjects/subjects.module.js";

@Module({
  imports: [
    AcademicSessionsModule,
    ClassSectionsModule,
    SubjectsModule,
    StaffModule,
  ],
  controllers: [SectionSubjectsController],
  providers: [SectionSubjectsService],
  exports: [SectionSubjectsService],
})
export class SectionSubjectsModule {}
