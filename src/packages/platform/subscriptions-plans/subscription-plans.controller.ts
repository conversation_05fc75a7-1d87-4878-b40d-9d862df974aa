import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
} from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import {
  CreateSubscriptionPlanDto,
  PackageDto,
  UpdateSubscriptionPlanDto,
} from "./dto/subscription-plans.dto.js";
import { SubscriptionPlansService } from "./subscription-plans.service.js";

import { ZodSerializerDto } from "nestjs-zod";
import {
  ApiDocCreateSubscriptionPlan,
  ApiDocDeleteSubscription,
  ApiDocGetSubscriptionPlanById,
  ApiDocGetAllSubscriptionPlans,
  ApiDocUpdateSubscriptionPlan,
} from "./docs/subscription-plans.docs.js";
import { Public } from "../../../shared/decorators/public.decorator.js";
import { ListAllEntitiesQueryDto } from "../../../shared/dto/query.dto.js";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import { IdParamDto } from "../../../shared/dto/param.dto.js";

@Controller("/platform/subscription-plans")
@ApiTags("Software Packages")
@ZodSerializerDto(PackageDto)
export class SubscriptionPlansController {
  public constructor(
    private readonly subscriptionPlanService: SubscriptionPlansService,
  ) {}

  @Public()
  @Get()
  @ApiDocGetAllSubscriptionPlans()
  public async getAll(@Query() query: ListAllEntitiesQueryDto) {
    return await this.subscriptionPlanService.getAll(query);
  }

  @Roles(["PLATFORM_ADMIN"])
  @Public()
  @Get(":id")
  @ApiDocGetSubscriptionPlanById()
  public async getUnique(@Param() { id }: IdParamDto) {
    return await this.subscriptionPlanService.findById(id);
  }

  @Roles(["PLATFORM_ADMIN"])
  @Post()
  @ApiDocCreateSubscriptionPlan()
  public async create(@Body() createPackageDto: CreateSubscriptionPlanDto) {
    return await this.subscriptionPlanService.create(createPackageDto);
  }

  @Roles(["PLATFORM_ADMIN"])
  @Patch(":id")
  @ApiDocUpdateSubscriptionPlan()
  public async update(
    @Param() { id }: IdParamDto,
    @Body() updateSubscriptionPlanDto: UpdateSubscriptionPlanDto,
  ) {
    return await this.subscriptionPlanService.update(
      id,
      updateSubscriptionPlanDto,
    );
  }

  @Roles(["PLATFORM_ADMIN"])
  @Delete(":id")
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiDocDeleteSubscription()
  public async delete(@Param() { id }: IdParamDto) {
    console.log(id);
    await this.subscriptionPlanService.delete(id);
  }
}
