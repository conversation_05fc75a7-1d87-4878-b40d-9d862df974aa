import { createZodDto } from "nestjs-zod";
import { z } from "zod";
import { getUuidSchema } from "../../../../shared/schema/zod-common.schema.js";
import { getApiResponseSchema } from "../../../../shared/schema/api-response.schema.js";

const subjectsBaseSchema = z.object({
  name: z
    .string()
    .nonempty()
    .max(100, { message: "Name cannot exceed 100 characters" }),
  type: z.enum(["THEORY", "PRACTICAL"], {
    message: "Type must be one of 'THEORY' or 'PRACTICAL'.",
  }),
  marks: z
    .number({ message: "Subject marks are required" })
    .min(1, { message: "Subject marks cannot be less than 1" })
    .max(1000, { message: "Subject marks cannot exceed 1000" }),
});

// ------------------- Create-Subject-Dto ------------------->

export class CreateSubjectDto extends createZodDto(subjectsBaseSchema) {}

// ------------------- Update-Subject-Dto ------------------->
export const updateSubjectSchema = subjectsBaseSchema.partial();
export class UpdateSubjectDto extends createZodDto(updateSubjectSchema) {}

// ------------------- Subject-Response-Dto ------------------->
/**
 * Subject response schema with complete subject information
 * Includes all subject fields returned from database operations
 */
export const subjectResponseSchema = subjectsBaseSchema.extend({
  id: getUuidSchema("Subject ID").describe("Unique identifier for the subject"),
  isActive: z.boolean().describe("Whether the subject is currently active"),
  academicSessionId: getUuidSchema("Academic Session ID").describe(
    "ID of the academic session this subject belongs to",
  ),
  createdAt: z.coerce
    .date()
    .describe("Date and time when the subject was created"),
});

export class SubjectResponseDto extends createZodDto(
  getApiResponseSchema(subjectResponseSchema),
) {}
