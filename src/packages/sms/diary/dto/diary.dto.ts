import { createZodDto } from "nestjs-zod";
import { z } from "zod";
import {
  getUuidSchema,
  dateSchema,
  listAllEntitiesQuerySchema,
} from "../../../../shared/schema/zod-common.schema.js";

const diaryBaseSchema = z.object({
  subjectId: getUuidSchema("Subject ID"),
  content: z.string().nonempty(),
  date: dateSchema,
});

// ------------------- Create-Diary-Dto ------------------->
export const createDiarySchema = diaryBaseSchema;
export class CreateDiaryDto extends createZodDto(diaryBaseSchema) {}

// ------------------- Diary-Response-Dto ------------------->
/**
 * Diary response schema with complete diary entry information
 * Includes subject details and class section information
 */
export const diaryResponseSchema = z.object({
  id: z.string().describe("Diary entry ID"),
  content: z.string().describe("Diary entry content"),
  date: z.coerce.date().describe("Date of the diary entry"),
  createdAt: z.coerce.date().describe("Creation timestamp"),
  subject: z
    .object({
      id: z.string().describe("Subject ID"),
      name: z.string().describe("Subject name"),
      type: z.enum(["THEORY", "PRACTICAL"]).describe("Subject type"),
      marks: z.number().describe("Subject marks"),
    })
    .describe("Subject information"),
  classSection: z
    .object({
      id: z.string().describe("Class section ID"),
      name: z.string().describe("Class section name"),
      class: z
        .object({
          id: z.string().describe("Class ID"),
          name: z.string().describe("Class name"),
        })
        .describe("Class information"),
    })
    .describe("Class section information"),
});

export class DiaryResponseDto extends createZodDto(diaryResponseSchema) {}

// ------------------- List-Diaries-Query-Dto ------------------->
/**
 * Query parameters for filtering diary entries
 * Supports date-based filtering and pagination
 */
export const listDiariesQuerySchema = listAllEntitiesQuerySchema
  .extend({
    date: dateSchema
      .optional()
      .describe("Filter by specific date (YYYY-MM-DD)"),
    month: z
      .string()
      .regex(/^\d{4}-\d{2}$/, "Invalid month format. Use YYYY-MM")
      .optional()
      .describe("Filter by specific month (YYYY-MM)"),
    from: dateSchema.optional().describe("Filter from date (YYYY-MM-DD)"),
    to: dateSchema.optional().describe("Filter to date (YYYY-MM-DD)"),
    subjectId: getUuidSchema("Subject ID")
      .optional()
      .describe("Filter by subject ID"),
  })
  .refine(
    data => {
      // Ensure 'from' is not later than 'to' when both are provided
      if (data.from && data.to) {
        return new Date(data.from) <= new Date(data.to);
      }
      return true;
    },
    {
      message: "From date must be earlier than or equal to to date",
      path: ["from"],
    },
  )
  .refine(
    data => {
      // Ensure only one type of date filter is used at a time
      const dateFilters = [data.date, data.month, data.from && data.to].filter(
        Boolean,
      );
      return dateFilters.length <= 1;
    },
    {
      message:
        "Only one date filter type can be used at a time (date, month, or from/to range)",
      path: ["date"],
    },
  );

export class ListDiariesQueryDto extends createZodDto(listDiariesQuerySchema) {}
