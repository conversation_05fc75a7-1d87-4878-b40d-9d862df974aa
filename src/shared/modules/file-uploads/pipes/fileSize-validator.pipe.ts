import {
  BadRequestException,
  Injectable,
  PipeTransform,
  UnprocessableEntityException,
} from "@nestjs/common";

@Injectable()
export class FileSizeValidatorPipe implements PipeTransform {
  private readonly maxFileSizeInByte = 1024 * 1024 * 20; // 20 MB

  transform(file?: Express.Multer.File) {
    if (!file) throw new BadRequestException("Image file is required");
    if (file.size > this.maxFileSizeInByte) {
      throw new UnprocessableEntityException(
        "Image size should be less than 20MB",
      );
    }
    return file;
  }
}
