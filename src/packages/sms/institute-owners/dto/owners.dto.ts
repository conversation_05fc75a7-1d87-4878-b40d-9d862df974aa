import { z } from "zod";
import {
  baseUserSchema,
  userProfileSchema,
} from "../../../../core/users/dto/user.dto.js";
import { createZodDto } from "nestjs-zod";

// ------------------- Create-Owner-Dto ------------------->
export const createOwnerSchema = baseUserSchema.extend({});
export class CreateOwnerDto extends createZodDto(createOwnerSchema) {}

// ------------------- Update-Owner-Dto ------------------->
export const updateOwnerSchema = createOwnerSchema.partial();
export class UpdateOwnerDto extends createZodDto(updateOwnerSchema) {}

// ------------------- Get-Owner-Dto ------------------->
export const ownerResponseSchema = userProfileSchema.extend({
  hasCompletedSetup: z.boolean({
    message: "hasCompletedSetup must be a boolean",
  }),
});
export class GetOwnerDto extends createZodDto(ownerResponseSchema) {}

// ------------------- Update-Owner-Profile-Setup-Dto ------------------->
// This is used to update the hasCompletedSetup field
export const updateOwnerProfileSetupSchema = ownerResponseSchema.pick({
  hasCompletedSetup: true,
});
