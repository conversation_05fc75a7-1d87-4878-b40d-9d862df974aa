import { Insertable, Selectable, Updateable } from "kysely";
import { InstituteTable } from "../../../../database/types.js";
import { instituteResponseSchema } from "../dto/institute.dto.js";
import { z } from "zod";

export type NewInstitute = Insertable<InstituteTable>;
export type InstituteUpdate = Updateable<Institute>;
export type Institute = Selectable<InstituteTable>;

export type InstituteResponse = z.infer<typeof instituteResponseSchema>;

export interface FindOwnerInstituteResponse {
  institute: InstituteResponse | null;
}
