import { CanActivate, ExecutionContext, Injectable } from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { Request } from "express";
import { IS_PUBLIC_KEY } from "../../../shared/decorators/public.decorator.js";
import { ROLES } from "../constants/roles.constants.js";
import { Role } from "../types/roles.types.js";

@Injectable()
export class RolesGuard implements CanActivate {
  public constructor(private readonly reflector: Reflector) {}

  public canActivate(context: ExecutionContext): boolean {
    const roleNames = this.reflector.getAllAndOverride<
      Role["name"][] | undefined
    >("roles", [context.getHandler(), context.getClass()]);

    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    const roleCodes = roleNames?.map(role => ROLES[role].code);

    if (!roleCodes || roleCodes.length === 0 || isPublic) return true;

    const user = context.switchToHttp().getRequest<Request>().user;

    return this.isUserHasAllowedRole(roleCodes, user.role);
  }

  private isUserHasAllowedRole(
    allowedRoleCode: Role["code"][],
    userRoleCode: Role["code"],
  ): boolean {
    return allowedRoleCode.includes(userRoleCode);
  }
}
