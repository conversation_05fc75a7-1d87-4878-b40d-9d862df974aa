import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  PipeTransform,
} from "@nestjs/common";
import { ROLES } from "../../roles/constants/roles.constants.js";
import { updateOwnerProfileSetupSchema } from "../../../packages/sms/institute-owners/dto/owners.dto.js";
import { validate } from "nestjs-zod";
import { ZodObject } from "zod";

@Injectable()
export class RoleBasedValidationPipe implements PipeTransform {
  constructor(private readonly role: number) {}

  transform(value: unknown) {
    let dtoClass: ZodObject<any>;

    switch (this.role) {
      case ROLES.INSTITUTE_OWNER.code:
        dtoClass = updateOwnerProfileSetupSchema;
        break;
      default:
        throw new ForbiddenException(
          "You do not have permission to access this resource",
        );
    }

    const errors = validate(value, dtoClass);

    if (errors.length > 0) {
      throw new BadRequestException(errors);
    }

    return value;
  }
}
