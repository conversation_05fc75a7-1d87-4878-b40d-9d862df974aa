import { sql, type Kysely } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable("institute")
    .addColumn("id", "uuid", col =>
      col.primaryKey().defaultTo(sql`gen_random_uuid()`),
    )
    .addColumn("name", "text", col => col.notNull().unique())
    .addColumn("email", "text", col => col.notNull().unique())
    .addColumn("logo", "text")
    .addColumn("isActive", "boolean", col => col.notNull().defaultTo(true))
    .addColumn("isBasicSetupComplete", "boolean", col =>
      col.notNull().defaultTo(false),
    )
    .addColumn("ownerId", "uuid", col => col.notNull().unique())
    .addColumn("createdAt", "timestamptz", col =>
      col.notNull().defaultTo(sql`(NOW())`),
    )
    .addForeignKeyConstraint(
      "institute_ownerId",
      ["ownerId"],
      "instituteOwner",
      ["userId"],
      cb => cb.onDelete("restrict").onUpdate("cascade"),
    )
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("institute").execute();
}
