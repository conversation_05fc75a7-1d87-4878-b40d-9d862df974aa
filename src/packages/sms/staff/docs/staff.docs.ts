import { ApiOperation, ApiResponse } from "@nestjs/swagger";
import {
  useCommonCreateResourceApiResponses,
  useCommonListResourceDocs,
} from "../../../../docs/shared/api-responses.js";
import { StaffProfileResponseDto } from "../dto/staff.dto.js";

export function ApiDocCreateStaff() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Create a new Staff",
      description: "Creates a new staff with the provided details",
    }),
    ApiResponse({
      status: 201,
      type: StaffProfileResponseDto,
      description: "Returns the newly created staff with complete details",
    }),
  );
}

export function ApiDocGetAllStaff() {
  return useCommonListResourceDocs(
    ApiOperation({
      summary: "Get all staff",
      description: "Returns all staff of the branch",
    }),
    ApiResponse({
      status: 200,
      type: StaffProfileResponseDto,
      isArray: true,
      description: "Returns the list of staff",
    }),
  );
}
