/* Ebridge API Documentation - Custom Swagger Theme
 * A clean, professional theme for Swagger UI
 */

/* ---- FONT & GENERAL STYLING ---- */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif;
  background-color: #f8f9fa;
}

.swagger-ui {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif !important;
  color: #333;
}

.swagger-ui .info li, .swagger-ui .info p, .swagger-ui .info table {
  font-size: 15px;
  line-height: 1.6;
}

/* ---- HEADER STYLING ---- */
.swagger-ui .topbar {
  background-color: #2c3e50;
  padding: 15px 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.swagger-ui .topbar .download-url-wrapper .download-url-button {
  background-color: #3498db;
  color: white;
  border-radius: 4px;
  border: none;
  box-shadow: none;
  transition: background-color 0.3s ease;
}

.swagger-ui .topbar .download-url-wrapper .download-url-button:hover {
  background-color: #2980b9;
}

.swagger-ui .topbar .download-url-wrapper input[type=text] {
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: none;
}

/* ---- API INFO SECTION ---- */
.swagger-ui .info {
  margin: 30px 0;
}

.swagger-ui .info .title {
  font-weight: 600;
  font-size: 32px;
  color: #2c3e50;
  margin-bottom: 10px;
}

.swagger-ui .info .description {
  font-size: 16px;
  color: #555;
}

.swagger-ui .info .title small.version-stamp {
  background-color: #3498db;
  color: white;
  font-size: 14px;
  padding: 3px 10px;
  border-radius: 15px;
  margin-left: 10px;
  font-weight: 500;
}

.swagger-ui .info .base-url {
  font-weight: 500;
  color: #3498db;
}

/* ---- TAGS & OPERATIONS ---- */
.swagger-ui .opblock-tag {
  font-size: 20px;
  color: #2c3e50;
  font-weight: 600;
  margin: 15px 0 10px 0;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.swagger-ui .opblock-tag:hover {
  background-color: transparent;
  transform: translateX(2px);
  transition: transform 0.2s ease;
}

/* Operation blocks */
.swagger-ui .opblock {
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  border: none;
  margin-bottom: 15px;
  transition: box-shadow 0.3s ease;
}

.swagger-ui .opblock:hover {
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
}

/* HTTP Methods styling */
.swagger-ui .opblock.opblock-get {
  background-color: rgba(97, 175, 254, 0.1);
  border-color: #61affe;
}

.swagger-ui .opblock.opblock-get .opblock-summary-method {
  background-color: #61affe;
}

.swagger-ui .opblock.opblock-put {
  background-color: rgba(252, 161, 48, 0.1);
  border-color: #fca130;
}

.swagger-ui .opblock.opblock-put .opblock-summary-method {
  background-color: #fca130;
}

.swagger-ui .opblock.opblock-post {
  background-color: rgba(73, 204, 144, 0.1);
  border-color: #49cc90;
}

.swagger-ui .opblock.opblock-post .opblock-summary-method {
  background-color: #49cc90;
}

.swagger-ui .opblock.opblock-delete {
  background-color: rgba(249, 62, 62, 0.1);
  border-color: #f93e3e;
}

.swagger-ui .opblock.opblock-delete .opblock-summary-method {
  background-color: #f93e3e;
}

.swagger-ui .opblock.opblock-patch {
  background-color: rgba(80, 227, 194, 0.1);
  border-color: #50e3c2;
}

.swagger-ui .opblock.opblock-patch .opblock-summary-method {
  background-color: #50e3c2;
}

.swagger-ui .opblock-summary-method {
  font-weight: 600;
  min-width: 80px;
  text-align: center;
  border-radius: 4px;
  text-shadow: none;
}

.swagger-ui .opblock-summary {
  padding: 8px 12px;
}

.swagger-ui .opblock-summary-path {
  font-weight: 500;
  font-size: 16px;
  color: #2c3e50;
}

.swagger-ui .opblock-summary-description {
  font-size: 14px;
  color: #777;
  font-weight: 400;
}

/* ---- MODELS & PARAMETERS ---- */
.swagger-ui .parameters-col_description {
  font-size: 14px;
  color: #555;
}

.swagger-ui .parameters-col_name {
  font-weight: 600;
  font-size: 14px;
  color: #2c3e50;
}

.swagger-ui table thead tr td, .swagger-ui table thead tr th {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 600;
  border-bottom: 1px solid #ddd;
  padding: 10px 0;
}

.swagger-ui .parameters-table tr td {
  padding: 15px 0;
  vertical-align: top;
}

.swagger-ui .model-title {
  font-weight: 600;
  color: #2c3e50;
}

.swagger-ui section.models {
  border-radius: 6px;
  margin: 20px 0;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.swagger-ui section.models .model-container {
  margin: 0;
  border-radius: 0;
  border: none;
  border-bottom: 1px solid #f0f0f0;
}

.swagger-ui section.models .model-container:last-child {
  border-bottom: none;
}

/* ---- RESPONSE SECTION ---- */
.swagger-ui .responses-table {
  border-radius: 4px;
  overflow: hidden;
}

.swagger-ui .response-col_status {
  font-weight: 600;
  color: #2c3e50;
}

.swagger-ui .response-col_description__inner span {
  font-size: 14px;
  color: #555;
}

/* Response codes styling */
.swagger-ui .responses-table .response-col_status {
  padding: 12px 20px !important;
  font-size: 14px;
}

.swagger-ui table tbody tr td:first-of-type {
  padding: 12px 20px;
}

/* Success responses */
.swagger-ui .responses-table tr.response[data-code^="2"] .response-col_status {
  color: #49cc90;
}

/* Redirect responses */
.swagger-ui .responses-table tr.response[data-code^="3"] .response-col_status {
  color: #fca130;
}

/* Client error responses */
.swagger-ui .responses-table tr.response[data-code^="4"] .response-col_status {
  color: #f93e3e;
}

/* Server error responses */
.swagger-ui .responses-table tr.response[data-code^="5"] .response-col_status {
  color: #d1335b;
}

/* ---- FORMS & BUTTONS ---- */
.swagger-ui button {
  border-radius: 4px;
  box-shadow: none;
  transition: all 0.3s ease;
}

.swagger-ui .btn {
  font-weight: 500;
  border-radius: 4px;
  padding: 8px 16px;
  transition: all 0.2s ease-in-out;
}

.swagger-ui .btn.execute {
  background-color: #3498db;
  border-color: #3498db;
  color: #fff;
}

.swagger-ui .btn.execute:hover {
  background-color: #2980b9;
  border-color: #2980b9;
}

.swagger-ui .btn.try-out__btn {
  border-color: #3498db;
  color: #3498db;
}

.swagger-ui .btn.try-out__btn:hover {
  background-color: #3498db;
  color: #fff;
}

.swagger-ui .btn.cancel {
  border-color: #f93e3e;
  color: #f93e3e;
}

.swagger-ui .btn.cancel:hover {
  background-color: #f93e3e;
  color: #fff;
}

/* ---- INPUT STYLING ---- */
.swagger-ui input[type=text], .swagger-ui textarea {
  border-radius: 4px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  box-shadow: none;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.swagger-ui input[type=text]:focus, .swagger-ui textarea:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 1px #3498db;
}

/* ---- AUTH STYLING ---- */
.swagger-ui .auth-wrapper .authorize {
  border-color: #3498db;
  color: #3498db;
}

.swagger-ui .auth-wrapper .authorize:hover {
  background-color: #3498db;
  color: #fff;
}

.swagger-ui .dialog-ux .modal-ux {
  border-radius: 6px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.swagger-ui .dialog-ux .modal-ux-header {
  border-bottom: 1px solid #eee;
  padding: 15px 20px;
}

.swagger-ui .dialog-ux .modal-ux-header h3 {
  font-weight: 600;
  color: #2c3e50;
}

.swagger-ui .dialog-ux .modal-ux-content {
  padding: 20px;
}

/* ---- SCHEMA STYLING ---- */
.swagger-ui .model-box {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.swagger-ui .model {
  font-size: 14px;
}

.swagger-ui .model-toggle {
  top: 0;
}

.swagger-ui .model-toggle:after {
  background-color: #3498db;
}

/* ---- MOBILE RESPONSIVE ADJUSTMENTS ---- */
@media (max-width: 768px) {
  .swagger-ui .opblock-summary-description {
    display: none;
  }
  
  .swagger-ui .opblock-summary-path {
    max-width: 180px;
    font-size: 14px;
  }
  
  .swagger-ui .opblock-summary-method {
    min-width: 60px;
    font-size: 12px;
  }
  
  .swagger-ui .info .title {
    font-size: 26px;
  }
}

/* ---- EBRIDGE SPECIFIC BRANDING ---- */
/* Replace with your actual brand color if different */
:root {
  --ebridge-primary: #3498db;
  --ebridge-secondary: #2c3e50;
}

/* Add your logo */
.swagger-ui .topbar .topbar-wrapper img {
  content: url('https://ebridge.com/logo.png');
  height: 40px;
}

/* Fallback if image doesn't load */
.swagger-ui .topbar-wrapper::before {
  content: 'Ebridge API';
  color: white;
  font-weight: 600;
  font-size: 20px;
  margin-right: 10px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Dark mode toggle - if you want to add this feature */
.dark-mode-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #2c3e50;
  color: white;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 9999;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Print optimizations */
@media print {
  .swagger-ui .opblock-body, .swagger-ui .opblock {
    break-inside: avoid;
  }
  
  .swagger-ui .topbar, .swagger-ui .information-container .btnRender {
    display: none !important;
  }
}