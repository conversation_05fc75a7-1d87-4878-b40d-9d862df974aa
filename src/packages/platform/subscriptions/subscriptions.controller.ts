import { Controller, Get, Query } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { SubscriptionsService } from "./subscriptions.service.js";
import { Roles } from "../../../core/roles/decorators/roles.decorator.js";
import { ListAllEntitiesQueryDto } from "../../../shared/dto/query.dto.js";

@Controller("subscriptions")
@ApiTags("Subscriptions")
export class SubscriptionsController {
  public constructor(
    private readonly subscriptionsService: SubscriptionsService,
  ) {}

  @Roles(["PLATFORM_ADMIN"])
  @Get()
  public async getAll(@Query() query: ListAllEntitiesQueryDto) {
    return await this.subscriptionsService.findAll(query);
  }
}
