import {
  ConflictException,
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
} from "@nestjs/common";
import { Kysely, Transaction } from "kysely";
import { PasswordService } from "../../shared/services/password.service.js";
import { InjectKysely } from "../../shared/modules/kysely/decorators/kysely.decorators.js";
import { DB } from "../../database/types.js";
import { NewUser, User, UserProfile } from "./types/users.types.js";
import { OwnersService } from "../../packages/sms/institute-owners/owners.service.js";
import { ROLES } from "../roles/constants/roles.constants.js";
import { Role } from "../roles/types/roles.types.js";
import {
  InstituteOwnerProfile,
  InstituteOwnerUpdate,
} from "../../packages/sms/institute-owners/types/owner.types.js";
import { StaffService } from "../../packages/sms/staff/staff.service.js";
import { Staff<PERSON>ro<PERSON>le } from "../../packages/sms/staff/types/staff.types.js";

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  public constructor(
    @InjectKysely() private readonly db: Kysely<DB>,
    private readonly passwordService: PasswordService,
    private readonly instituteOwnersService: OwnersService,
    private readonly staffService: StaffService,
  ) {}

  public async findByEmailAndIncludePassword(
    email: User["email"],
    trx?: Transaction<DB>,
  ): Promise<User | undefined> {
    const kyselyClient = trx ?? this.db;
    try {
      return await kyselyClient
        .selectFrom("users")
        .where("email", "=", email)
        .selectAll()
        .executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error("Failed to get user by email", error);
      throw error;
    }
  }

  public async updateProfile(
    userId: User["id"],
    roleCode: Role["code"],
    data: InstituteOwnerUpdate,
  ) {
    await this.checkExistingUser(userId);
    try {
      switch (roleCode) {
        case ROLES.INSTITUTE_OWNER.code: {
          await this.instituteOwnersService.update(userId, data);
          break;
        }
        default: {
          throw new ForbiddenException(
            "You do not have permission to access this resource",
          );
        }
      }
    } catch (error: unknown) {
      this.logger.error("Failed to update user");
      throw error;
    }
  }

  public async getProfile(
    userId: User["id"],
    roleCode: Role["code"],
  ): Promise<UserProfile | InstituteOwnerProfile | StaffProfile> {
    try {
      switch (roleCode) {
        case ROLES.INSTITUTE_OWNER.code: {
          const owner = await this.instituteOwnersService.findUniqueOwner({
            userId,
          });
          if (!owner) {
            throw new NotFoundException(`Owner profile not found`);
          }
          return owner;
        }
        case ROLES.TEACHER.code: {
          const staff = await this.staffService.getTeacherProfile(userId);
          return staff;
        }
        default: {
          const user = await this.findById(userId);
          if (!user) {
            throw new NotFoundException(
              `User with id: '${userId}' does not exist`,
            );
          }
          return user;
        }
      }
    } catch (error: unknown) {
      if (!(error instanceof NotFoundException)) {
        this.logger.error(
          `Failed to get user profile for user: ${userId}`,
          error,
        );
      }
      throw error;
    }
  }

  public async findById(id: User["id"]): Promise<UserProfile | undefined> {
    try {
      return await this.selectUserQuery()
        .where("users.id", "=", id)
        .executeTakeFirst();
    } catch (error: unknown) {
      this.logger.error(`Failed to get user by id: ${id}`, error);
      throw error;
    }
  }

  public async create(
    user: NewUser,
    trx?: Transaction<DB>,
  ): Promise<UserProfile> {
    const duplicateUser = await this.findByEmailAndIncludePassword(
      user.email,
      trx,
    );

    if (duplicateUser) {
      throw new ConflictException("This email is already registered");
    }
    try {
      const hashPassword = await this.passwordService.hash(user.password);

      return await this.db.transaction().execute(async userRoleTransaction => {
        const kyselyClient = trx ?? userRoleTransaction;

        return await kyselyClient
          .with("new_user", db =>
            db
              .insertInto("users")
              .values({
                ...user,
                password: hashPassword,
              })
              .returning([...this.safeUserProperties(), "roleId"]),
          )
          .with("role", db =>
            db
              .selectFrom("role")
              .select("code")
              .where("id", "=", db.selectFrom("new_user").select("roleId")),
          )
          .selectFrom(["new_user", "role"])
          .select([...this.safeUserProperties(), "role.code as role"])
          .executeTakeFirstOrThrow();
      });
    } catch (error) {
      this.logger.error("Failed to create user");
      throw error;
    }
  }

  public async updateUserPassword(
    userId: User["id"],
    data: Pick<User, "password" | "isPasswordTemporary">,
  ) {
    await this.checkExistingUser(userId);

    try {
      const hashPassword = await this.passwordService.hash(data.password);
      return await this.db
        .updateTable("users")
        .set("password", hashPassword)
        .set("isPasswordTemporary", data.isPasswordTemporary)
        .where("id", "=", userId)
        .returning(this.safeUserProperties())
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      this.logger.error("Failed to update user");
      throw error;
    }
  }

  public async createWithTempPassword(
    user: Omit<NewUser, "password">,
    trx?: Transaction<DB>,
  ) {
    const randomPassword = this.passwordService.generateRandomPassword();
    try {
      const createdUser = await this.create(
        {
          ...user,
          password: randomPassword,
          isPasswordTemporary: true,
        },
        trx,
      );
      return { ...createdUser, tempPassword: randomPassword };
    } catch (error: unknown) {
      this.logger.error("Failed to create user with temp password");
      throw error;
    }
  }

  private async checkExistingUser(userId: User["id"]) {
    let existingUser: UserProfile | undefined;

    try {
      existingUser = await this.findById(userId);
    } catch (error: unknown) {
      this.logger.error("Failed to check if user exists", error);
      throw error;
    }

    if (!existingUser) {
      throw new NotFoundException(`User with id: '${userId}' does not exist`);
    }
  }

  private selectUserQuery() {
    return this.db
      .selectFrom("users")
      .innerJoin("role", "users.roleId", "role.id")
      .select([
        "users.id",
        "users.email",
        "users.name",
        "users.createdAt",
        "users.id",
        "users.isPasswordTemporary",
        "users.isActive",
        "users.address",
        "users.cnic",
        "users.gender",
        "users.phone",
        "users.photo",
        "role.code as role",
      ]);
  }

  private safeUserProperties() {
    return [
      "email",
      "name",
      "createdAt",
      "id",
      "isPasswordTemporary",
      "isActive",
      "address",
      "cnic",
      "gender",
      "phone",
      "photo",
    ] as const;
  }
}
