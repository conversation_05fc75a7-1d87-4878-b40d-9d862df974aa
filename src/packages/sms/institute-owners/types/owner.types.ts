import { z } from "zod";
import { createOwnerSchema, ownerResponseSchema } from "../dto/owners.dto.js";
import { Insertable, Selectable, Updateable } from "kysely";
import { InstituteOwnerTable } from "../../../../database/types.js";

export type InstituteOwner = Selectable<InstituteOwnerTable>;
export type InstituteOwnerUpdate = Updateable<InstituteOwnerTable>;
export type NewInstituteOwner = Insertable<InstituteOwnerTable>;

export type CreateOwnerPayload = z.infer<typeof createOwnerSchema>;

export type InstituteOwnerProfile = z.infer<typeof ownerResponseSchema>;

// Criteria to find unique owner
export type UniqueOwnerCriteria = Partial<
  Pick<InstituteOwnerProfile, "email" | "cnic" | "phone" | "createdAt"> & {
    userId: string;
  }
>;
