import { Module } from "@nestjs/common";
import { InstitutesController } from "./institutes.controller.js";
import { InstitutesService } from "./institutes.service.js";
import { InstituteOwnersModule } from "../institute-owners/owners.module.js";

@Module({
  controllers: [InstitutesController],
  providers: [InstitutesService],
  imports: [InstituteOwnersModule],
  exports: [InstitutesService],
})
export class InstitutesModule {}
