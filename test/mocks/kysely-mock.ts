import { Provider } from "@nestjs/common";
import { KYSELY_MODULE_CONNECTION_TOKEN } from "../../src/shared/modules/kysely/constants/kysely.constants.js";

const kyselyClientMock = {
  selectFrom: vi.fn().mockReturnThis(),
  selectAll: vi.fn().mockReturnThis(),
};

kyselyClientMock.selectFrom();
export const KyselyClientMockProvider: Provider = {
  provide: KYSELY_MODULE_CONNECTION_TOKEN,
  useValue: kyselyClientMock,
};
