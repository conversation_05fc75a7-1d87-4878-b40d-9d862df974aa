import { Injectable, Logger } from "@nestjs/common";
import { Kysely } from "kysely";
import { InjectKysely } from "../../../shared/modules/kysely/decorators/kysely.decorators.js";
import { ListAllEntitiesQueryOptions } from "../../../shared/types/shared.types.js";
import { DB } from "../../../database/types.js";
import { NewSubscription } from "./types/subscription.types.js";

@Injectable()
export class SubscriptionsService {
  private readonly logger = new Logger(SubscriptionsService.name);

  public constructor(@InjectKysely() private readonly db: Kysely<DB>) {}

  public async findAll(queryOptions: ListAllEntitiesQueryOptions) {
    try {
      return await this.db
        .selectFrom("subscription")
        .selectAll()
        .limit(queryOptions.limit)
        .offset(queryOptions.offset)
        .orderBy("createdAt", "asc")
        .execute();
    } catch (error: unknown) {
      this.logger.error("Failed to get all subscriptions", error);
      throw error;
    }
  }

  public async create(data: NewSubscription) {
    try {
      return await this.db
        .insertInto("subscription")
        .values(data)
        .returningAll()
        .executeTakeFirstOrThrow();
    } catch (error: unknown) {
      this.logger.error("Failed to create subscription", error);
      throw error;
    }
  }
}
