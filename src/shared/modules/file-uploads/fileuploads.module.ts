import { Modu<PERSON> } from "@nestjs/common";
import { FileUploadController } from "./fileuploads.controller.js";
import { FileUploadService } from "./fileuploads.service.js";
import { MulterModule } from "@nestjs/platform-express";
import { diskStorage } from "multer";

@Module({
  imports: [
    MulterModule.register({
      storage: diskStorage({
        destination: "./uploads",
        filename: (req, file, cb) => {
          const fileName = `${Date.now().toString()}-${file.originalname.replace(/\s+/g, "-")}`; //Appending extension
          cb(null, fileName);
        },
      }),
    }),
  ],
  providers: [FileUploadService],
  controllers: [FileUploadController],
})
export class FileUploadModule {}
