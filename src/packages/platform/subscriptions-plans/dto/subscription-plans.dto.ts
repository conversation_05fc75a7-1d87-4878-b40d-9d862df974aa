import { z } from "zod";
import { createZodDto } from "nestjs-zod";
import { MAX_PRIZE_VALUE } from "../../../../shared/constants/dto.constants.js";

const basePackageSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().optional(),
  price: z
    .number()
    .min(0)
    .max(MAX_PRIZE_VALUE, {
      message: `Price cannot exceed ${MAX_PRIZE_VALUE.toString()}`,
    }),
  setupCharges: z
    .number()
    .min(1)
    .max(MAX_PRIZE_VALUE, {
      message: `Setup Charges cannot exceed ${MAX_PRIZE_VALUE.toString()}`,
    }),
  features: z.array(z.string()),
  branches: z.number().min(1).max(10, {
    message: "Branches must be between 1 and 10",
  }),
  students: z.number().min(1).max(10000, {
    message: "Students cannot exceed 10,000",
  }),
});

// --------------- Response Package Dto --------------->
export class PackageDto extends createZodDto(
  basePackageSchema.extend({
    description: z.string().nullable(),
    createdAt: z.date(),
  }),
) {}

// --------------- Create Package Dto --------------->
export class CreateSubscriptionPlanDto extends createZodDto(
  basePackageSchema.omit({ id: true }),
) {}

// -------------- Update Package Dto --------------->
export class UpdateSubscriptionPlanDto extends createZodDto(
  basePackageSchema.omit({ id: true }).partial(),
) {}
