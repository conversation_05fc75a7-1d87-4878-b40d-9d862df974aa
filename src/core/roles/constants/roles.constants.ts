import { NewRole } from "../types/roles.types.js";

export const PLATFORM_ROLES = {
  PLATFORM_ADMIN: {
    name: "PLATFORM_ADMIN",
    description: "Admin of panel",
    code: 3005,
  },
} as const;

export const SMS_ROLES = {
  INSTITUTE_OWNER: {
    name: "INSTITUTE_OWNER",
    description: "INSTITUTE_OWNER OF SCHOOL",
    code: 4442,
  },
  BRANCH_ADMIN: {
    name: "BRANCH_ADMIN",
    description: "Admin of Branch",
    code: 4443,
  },
  SUPPORT_STAFF: {
    name: "SUPPORT_STAFF",
    description: "Support staff of school",
    code: 4446,
  },
  GUARDIAN: {
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    description: "Parent of student",
    code: 4447,
  },
  ACCOUNTANT: {
    name: "ACCOUNTA<PERSON>",
    description: "Accountant of school",
    code: 4448,
  },
  STUDENT: {
    name: "STUDENT",
    description: "Student of school",
    code: 4444,
  },
  TEACHER: {
    name: "TEACHER",
    description: "Teacher of school",
    code: 4449,
  },
} as const;

export const ROLES: Record<NewRole["name"], NewRole> = {
  ...PLATFORM_ROLES,
  ...SMS_ROLES,
};
