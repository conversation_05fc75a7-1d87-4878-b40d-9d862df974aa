import { ArgumentsHost, Catch, HttpStatus, Logger } from "@nestjs/common";
import { PostgresException } from "../../utils/exception.util.js";
import type { Response } from "express";
import { BaseExceptionFilter } from "@nestjs/core";
import {
  createBadRequestResponse,
  createConflictResponse,
  createForbiddenResponse,
  createInternalServerErrorResponse,
} from "../../utils/api-responses.utils.js";

@Catch(PostgresException)
export class PostgresExceptionFilter extends BaseExceptionFilter {
  private readonly logger = new Logger(PostgresExceptionFilter.name);

  public catch(exception: PostgresException, host: ArgumentsHost) {
    const errorCode = exception.code;

    const ctx = host.switchToHttp();
    const res = ctx.getResponse<Response>();

    switch (errorCode) {
      // Unique violation - trying to insert duplicate key
      case "23505":
        res
          .status(HttpStatus.CONFLICT)
          .json(
            createConflictResponse(
              "A resource with the same unique identifier already exists.",
            ),
          );
        break;

      // Foreign key violation - invalid relation
      case "23503":
        res
          .status(HttpStatus.BAD_REQUEST)
          .json(
            createBadRequestResponse(
              "Invalid input syntax. Please check the foreign key.",
            ),
          );
        break;

      // Not-null violation - required field is null
      case "23502":
        res
          .status(HttpStatus.BAD_REQUEST)
          .json(
            createBadRequestResponse(
              "A required field is missing. Please provide all necessary data.",
            ),
          );
        break;

      // Check constraint violation - business rule failed
      case "23514":
        res
          .status(HttpStatus.BAD_REQUEST)
          .json(
            createBadRequestResponse(
              "Input data failed to meet the required conditions.",
            ),
          );
        break;

      // Invalid text representation (e.g. passing string to int)
      case "22P02":
        res
          .status(HttpStatus.BAD_REQUEST)
          .json(
            createBadRequestResponse(
              "Invalid input syntax. Please ensure the data is correctly formatted.",
            ),
          );
        break;

      // Numeric value out of range
      case "22003":
        res
          .status(HttpStatus.BAD_REQUEST)
          .json(
            createBadRequestResponse(
              "Invalid input syntax. Please ensure the data is correctly formatted.",
            ),
          );
        break;

      // Division by zero
      case "22012":
        res
          .status(HttpStatus.BAD_REQUEST)
          .json(
            createBadRequestResponse("Invalid operation: division by zero."),
          );
        break;

      // Insufficient privilege
      case "42501":
        res
          .status(HttpStatus.FORBIDDEN)
          .json(
            createForbiddenResponse(
              "You do not have permission to perform this action.",
            ),
          );
        break;

      // Undefined column
      case "42703":
        res
          .status(HttpStatus.INTERNAL_SERVER_ERROR)
          .json(
            createInternalServerErrorResponse(
              "A required field was not found in the database. Please contact support.",
            ),
          );
        break;

      default:
        this.logger.log(
          `An unexpected database error occurred. Error code: ${errorCode}`,
        );
        res
          .status(HttpStatus.INTERNAL_SERVER_ERROR)
          .json(
            createInternalServerErrorResponse(
              "An unexpected database error occurred. Please contact support.",
            ),
          );
    }
  }
}
