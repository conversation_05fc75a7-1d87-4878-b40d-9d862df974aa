import { <PERSON>, Get, Req } from "@nestjs/common";
import { UsersService } from "./users.service.js";
import { ApiTags, ApiExtraModels } from "@nestjs/swagger";
import type { Request } from "express";
import {
  BaseUserProfileResponseDto,
  InstituteOwnerProfileResponseDto,
  TeacherProfileResponseDto,
} from "./dto/user-profile-response.dto.js";
import { ApiDocGetCurrentUserProfile } from "./docs/users.docs.js";

@Controller()
@ApiTags("Users")
@ApiExtraModels(
  BaseUserProfileResponseDto,
  InstituteOwnerProfileResponseDto,
  TeacherProfileResponseDto,
)
export class UsersController {
  public constructor(private readonly usersService: UsersService) {}

  /**
   * Get current authenticated user's profile
   * Returns different profile structures based on user role
   */
  @Get(["sms/users/me", "platform/users/me"])
  @ApiDocGetCurrentUserProfile()
  public async getCurrentUserProfile(@Req() req: Request) {
    return await this.usersService.getProfile(req.user.sub, req.user.role);
  }
}
