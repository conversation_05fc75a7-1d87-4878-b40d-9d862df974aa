import { createZodDto } from "nestjs-zod";
import { listAllEntitiesQuerySchema } from "../../../../shared/schema/zod-common.schema.js";
import { staffTypeSchema } from "./staff.dto.js";

// ------------------- Staff-Query-Dto ------------------->
export const listStaffQuerySchema = listAllEntitiesQuerySchema.extend({
  type: staffTypeSchema.optional(),
});

export class ListStaffQueryDto extends createZodDto(listStaffQuerySchema) {}
