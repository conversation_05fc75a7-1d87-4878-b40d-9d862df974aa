import { ConflictException, HttpException, Logger } from "@nestjs/common";
import { isPostgresException, PostgresException } from "./exception.util.js";

/*
 * Extracts the column name from the constraint detail message.
 * e.g "Key (cnic)=(039740934739) already exists." to "cnic"
 */
export function extractViolatedColumnFromUniqueConstraint(
  error: PostgresException,
): string | undefined {
  if (!error.detail) return;

  return error.detail.match(/(?<=Key \()[^)]+/g)?.[0];
}

interface HandleExceptionOptions {
  logger?: Logger;
  resource: string;
  messages?: {
    uniqueConstraint?: string;
  };
}

export function handleDatabaseInsertException(
  error: unknown,
  options: HandleExceptionOptions,
): never {
  const logger = options.logger ?? new Logger("Service Exception");

  if (error instanceof HttpException) {
    throw error;
  }

  if (!isPostgresException(error)) {
    logger.log("Non postgres error caught in exception handler", error);
    throw error;
  }

  switch (error.code) {
    case "23505": {
      const constraintName = extractViolatedColumnFromUniqueConstraint(error);
      const message =
        options.messages?.uniqueConstraint ??
        `${options.resource} with same ${constraintName ?? "details"} already exists`;
      throw new ConflictException(message);
    }
    default:
      logger.log(
        "Unknown database error caught in database exception handler",
        error,
      );
      throw error;
  }
}
