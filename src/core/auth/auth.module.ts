import { Modu<PERSON> } from "@nestjs/common";
import { AuthService } from "./auth.service.js";
import { UsersModule } from "../users/users.module.js";
import { AuthController } from "./auth.controller.js";
import { RolesModule } from "../roles/roles.module.js";
import { PlatformAdminsModule } from "../../packages/platform/platform-admins/platform-admins.module.js";

@Module({
  imports: [UsersModule, RolesModule, PlatformAdminsModule],
  controllers: [AuthController],
  providers: [AuthService],
})
export class AuthModule {}
