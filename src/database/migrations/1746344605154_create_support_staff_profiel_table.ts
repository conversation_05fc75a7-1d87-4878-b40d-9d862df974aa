import { type Kysely, sql } from "kysely";

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable("supportStaffProfile")
    .addColumn("id", "uuid", col =>
      col.primary<PERSON>ey().defaultTo(sql`gen_random_uuid()`),
    )
    .addColumn("name", "text", col => col.notNull())
    .addColumn("email", "text", col => col.notNull().unique())
    .addColumn("phone", "text", col => col.notNull().unique())
    .addColumn("address", "text", col => col.notNull())
    .addColumn("gender", sql`gender`, col => col.notNull())
    .addColumn("photo", "text")
    .addColumn("cnic", "text", col => col.notNull().unique())
    .addColumn("createdAt", "timestamptz", col =>
      col.notNull().defaultTo("NOW()"),
    )
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("supportStaffProfile").execute();
}
