/* eslint-disable @typescript-eslint/no-unnecessary-type-arguments */
import { Abstract, ModuleMetadata, Type } from "@nestjs/common";
import { DrizzleConfig } from "drizzle-orm";
import { Options, PostgresType } from "postgres";

type InjectType = (
  | string
  | symbol
  | Type<any>
  | Abstract<any>
  | (() => void)
)[];

export interface DrizzleModuleAsyncOptions
  extends Pick<ModuleMetadata, "imports"> {
  inject?: InjectType;
  useFactory: (
    ...args: any[]
  ) => Promise<DrizzlePostgresConfig> | DrizzlePostgresConfig;
}

export interface DrizzlePostgresConfig {
  postgres: {
    url: string;
    config?: Options<Record<string, PostgresType<any>>> | undefined;
  };
  config?: DrizzleConfig<any> | undefined;
}
