import { ApiOperation, ApiResponse } from "@nestjs/swagger";
import { applyDecorators } from "@nestjs/common";
import { GetOwnerDto } from "../dto/owners.dto.js";
import { useCommonCreateResourceApiResponses } from "../../../../docs/shared/api-responses.js";

export function ApiDocCreateOwner() {
  return useCommonCreateResourceApiResponses(
    ApiOperation({
      summary: "Create a new Owner",
      description: "Creates a new owner with the provided details",
    }),
    ApiResponse({
      status: 201,
      type: GetOwnerDto,
      description: "Returns the newly created owner with complete details",
    }),
  );
}

export function ApiDocGetAllOwners() {
  return applyDecorators(
    ApiOperation({
      summary: "Get all owners",
      description: "Returns all owners",
    }),
    ApiResponse({
      status: 200,
      type: [GetOwnerDto],
      description: "Returns the list of owners",
    }),
  );
}
