import { Insertable, Selectable, Updateable } from "kysely";
import { StudentTable } from "../../../../database/types.js";
import { createStudentSchema } from "../dto/students.dto.js";
import { z } from "zod";

export type Student = Selectable<StudentTable>;
export type NewStudent = Insertable<StudentTable>;
export type StudentUpdate = Updateable<StudentTable>;

export type CreateStudentPayload = z.infer<typeof createStudentSchema> & {
  academicSessionId: string;
};
