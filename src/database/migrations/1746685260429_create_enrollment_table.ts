import { type Kysely, sql } from "kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  await createEnrollmentType(db);
  await createEnrollmentStatusType(db);
  await createEnrollmentTable(db);
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("enrollment").execute();
  await db.schema.dropType("enrollmentStatus").execute();
  await db.schema.dropType("enrollmentType").execute();
}

async function createEnrollmentTable(db: Kysely<any>) {
  try {
    await db.schema
      .createTable("enrollment")
      .addColumn("id", "uuid", col =>
        col.primaryKey().defaultTo(sql`gen_random_uuid()`),
      )
      .addColumn("studentId", "uuid", col =>
        col
          .notNull()
          .references("student.id")
          .onDelete("restrict")
          .onUpdate("cascade"),
      )
      .addColumn("classSectionId", "uuid", col =>
        col
          .notNull()
          .references("classSection.id")
          .onDelete("restrict")
          .onUpdate("cascade"),
      )
      .addColumn("academicSessionId", "uuid", col =>
        col
          .notNull()
          .references("academicSession.id")
          .onDelete("restrict")
          .onUpdate("cascade"),
      )
      .addColumn("type", sql`enrollment_type`, col => col.notNull())
      .addColumn("status", sql`enrollment_status`, col => col.notNull())
      .addColumn("date", "date", col => col.notNull())
      .addColumn("createdAt", "timestamptz", col =>
        col.notNull().defaultTo(sql`NOW()`),
      )
      .addUniqueConstraint("unique_student_session_per_enrollment", [
        "studentId",
        "academicSessionId",
      ])
      .execute();

    await createUniqueIndexForNonRepeatingStudentPerSection(db);
    await createUniqueIndexForActiveStatusPerSession(db);
  } catch (error: unknown) {
    console.error("Failed to create enrollment table", error);
    throw error;
  }
}

async function createUniqueIndexForNonRepeatingStudentPerSection(
  db: Kysely<any>,
) {
  try {
    await db.schema
      .createIndex("idx_unique_student_per_section_if_not_repeating")
      .on("enrollment")
      .columns(["studentId", "classSectionId", "type"])
      .where("type", "!=", "REPEATING")
      .unique()
      .execute();
  } catch (error) {
    console.log(
      "Failed to create unique index for non repeating student per section",
      error,
    );
  }
}

async function createUniqueIndexForActiveStatusPerSession(db: Kysely<any>) {
  try {
    await db.schema
      .createIndex("idx_unique_academicSession_status")
      .on("enrollment")
      .columns(["academicSessionId", "status"])
      .where("status", "=", "ACTIVE")
      .unique()
      .execute();
  } catch (error: unknown) {
    console.log(
      "Failed to create unique index for active status per session",
      error,
    );
  }
}

async function createEnrollmentStatusType(db: Kysely<any>) {
  try {
    await db.schema
      .createType("enrollmentStatus")
      .asEnum([
        "ACTIVE",
        "EXPELLED",
        "GRADUATED",
        "DECEASED",
        "COMPLETED",
        "WITHDRAWN",
      ])
      .execute();
  } catch (error: unknown) {
    console.error("Failed to create enrollment status", error);
    throw error;
  }
}

async function createEnrollmentType(db: Kysely<any>) {
  try {
    await db.schema
      .createType("enrollmentType")
      .asEnum(["ADMISSION", "TRANSFER_IN", "REPEATING", "PROMOTION"])
      .execute();
  } catch (err: unknown) {
    console.error("Failed to create enrollment type", err);
    throw err;
  }
}
