import { createZodDto } from "nestjs-zod";
import { z } from "zod";
import {
  imageUrlSchema,
  getUuidSchema,
} from "../../../../shared/schema/zod-common.schema.js";

const baseInstituteSchema = z.object({
  name: z.string().max(100, { message: "Name cannot exceed 100 characters" }),
  email: z.string().email(),
  isActive: z.boolean().default(true),
  isBasicSetupComplete: z.boolean().default(false),
  logo: imageUrlSchema.optional().nullable(),
});

// ------------------- Create-Institute-Dto ------------------->
export class createInstituteDto extends createZodDto(baseInstituteSchema) {}

// ------------------- Update-Institute-Dto ------------------->
const updateInstituteSchema = baseInstituteSchema.partial();
export class UpdateInstituteDto extends createZodDto(updateInstituteSchema) {}

// ------------------- Institute-Response-Dto ------------------->
export const instituteResponseSchema = baseInstituteSchema.extend({
  id: getUuidSchema("Institute ID"),
  createdAt: z.date(),
  ownerId: getUuidSchema("Owner ID"),
});
export class InstituteResponseDto extends createZodDto(
  instituteResponseSchema,
) {}

// ------------------- Find-LoggedInOwner-Institute-Response-Dto ------------------->
export const currentUserInstituteResponseSchema = z.object({
  institute: instituteResponseSchema.nullable(),
});

export class CurrentUserInstituteResponseDto extends createZodDto(
  currentUserInstituteResponseSchema,
) {}
