import type { Kysely } from "kysely";
import { PLATFORM_ADMIN } from "../../core/users/constants/users.constant.js";
import { DB } from "../types.js";

export async function seed(db: Kysely<DB>): Promise<void> {
  try {
    const { id: superAdminRoleId } = await db
      .selectFrom("role")
      .selectAll()
      .where("name", "=", "PLATFORM_ADMIN")
      .executeTakeFirstOrThrow();

    await db.transaction().execute(async trx => {
      await trx
        .insertInto("users")
        .values({
          ...PLATFORM_ADMIN,
          roleId: superAdminRoleId,
        })
        .onConflict(oc => oc.doNothing())
        .returning("id")
        .execute();

      const { id: userId } = await trx
        .selectFrom("users")
        .selectAll()
        .where("email", "=", PLATFORM_ADMIN.email)
        .executeTakeFirstOrThrow();

      await trx
        .insertInto("platformAdmin")
        .values({ userId })
        .onConflict(oc => oc.doNothing())
        .executeTakeFirstOrThrow();
    });
  } catch (error: unknown) {
    console.error("Failed to seed platform admins data", error);
    throw error;
  }
}
