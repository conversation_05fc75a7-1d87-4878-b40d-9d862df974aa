import { createZodDto } from "nestjs-zod";
import { z } from "zod";
import { getUuidSchema } from "../../../../shared/schema/zod-common.schema.js";

// ------------------- Create-Staff-Param-Dto ------------------->

export const staffParamSchema = z.object({
  branchId: getUuidSchema("Branch Id param"),
});

export class CreateStaffParamDto extends createZodDto(staffParamSchema) {}

// ------------------- Update-Staff-Param-Dto ------------------->
export class UpdateStaffParamDto extends createZodDto(
  staffParamSchema.extend({ staffId: getUuidSchema("Staff Id param") }),
) {}
