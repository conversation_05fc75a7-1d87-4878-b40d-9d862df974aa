import { Insertable, Selectable, Updateable } from "kysely";
import { GuardianTable } from "../../../../database/types.js";
import { createGuardianSchema } from "../dto/guardian.dto.js";
import { z } from "zod";
import { UserProfile } from "../../../../core/users/types/users.types.js";

export type Guardian = Selectable<GuardianTable>;
export type NewGuardian = Insertable<GuardianTable>;
export type GuardianUpdate = Updateable<GuardianTable>;

export type CreateGuardianPayload = z.infer<typeof createGuardianSchema>;

export type GuardianProfile = UserProfile & {
  relation: Guardian["relation"];
};
